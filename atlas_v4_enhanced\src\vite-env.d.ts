/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_STREAM_API_KEY: string
  readonly VITE_STREAM_USER_ID: string
  readonly VITE_STREAM_USER_NAME: string
  readonly VITE_STREAM_USER_TOKEN: string
  readonly VITE_NODE_ENV: string
  readonly VITE_DEBUG_MODE: string
  readonly VITE_ENABLE_GROK_AI: string
  readonly VITE_ENABLE_REAL_TIME_SCANNER: string
  readonly VITE_ENABLE_NEWS_INSIGHTS: string
  readonly VITE_ENABLE_VOICE_INPUT: string
  readonly VITE_DEFAULT_THEME: string
  readonly VITE_ENABLE_THEME_SWITCHING: string
  readonly VITE_WEBSOCKET_RECONNECT_INTERVAL: string
  readonly VITE_API_TIMEOUT: string
  readonly VITE_CHAT_MESSAGE_LIMIT: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
