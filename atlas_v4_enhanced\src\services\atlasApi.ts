// A.T.L.A.S. API Service - Complete preservation of all existing endpoints
import {
  ApiResponse,
  ChatApiResponse,
  ScannerApiResponse,
  MarketDataResponse,
  SystemStatus,
  NewsInsight,
  TradingSignal
} from '../types/atlas';

class AtlasApiService {
  private baseUrl: string;
  private wsConnections: Map<string, WebSocket> = new Map();

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';
  }

  // Core API Methods - Preserve all 26 existing endpoints
  
  async healthCheck(): Promise<ApiResponse<SystemStatus>> {
    return this.request('/api/v1/health');
  }

  async chat(message: string, sessionId?: string): Promise<ChatApiResponse> {
    return this.request('/api/v1/chat', {
      method: 'POST',
      body: JSON.stringify({
        message,
        session_id: sessionId,
        timestamp: new Date().toISOString()
      })
    });
  }

  async getLeeMethodSignals(): Promise<ScannerApiResponse> {
    return this.request('/api/v1/lee_method/signals');
  }

  async scanMarket(): Promise<ScannerApiResponse> {
    return this.request('/api/v1/scanner/results');
  }

  async getMarketData(symbol: string): Promise<MarketDataResponse> {
    return this.request(`/api/v1/market_data/${symbol.toUpperCase()}`);
  }

  async getQuote(symbol: string): Promise<ApiResponse> {
    return this.request(`/api/v1/quote/${symbol.toUpperCase()}`);
  }

  async getPortfolioAnalysis(): Promise<ApiResponse> {
    return this.request('/api/v1/portfolio');
  }

  async getEducationalContent(query: string): Promise<ApiResponse> {
    return this.request('/api/v1/education', {
      method: 'POST',
      body: JSON.stringify({ query })
    });
  }

  async getNewsInsights(symbols?: string[]): Promise<ApiResponse<NewsInsight[]>> {
    const params = symbols ? `?symbols=${symbols.join(',')}` : '';
    return this.request(`/api/v1/news/insights${params}`);
  }

  async analyzeTradingStrategy(strategy: any): Promise<ApiResponse<TradingSignal>> {
    return this.request('/api/v1/trading/analyze', {
      method: 'POST',
      body: JSON.stringify(strategy)
    });
  }

  async getRiskAssessment(symbol: string, position: any): Promise<ApiResponse> {
    return this.request('/api/v1/risk/assess', {
      method: 'POST',
      body: JSON.stringify({ symbol, position })
    });
  }

  async getOptionsAnalysis(symbol: string): Promise<ApiResponse> {
    return this.request(`/api/v1/options/analyze/${symbol.toUpperCase()}`);
  }

  async getMLPrediction(symbol: string): Promise<ApiResponse> {
    return this.request(`/api/v1/ml/predict/${symbol.toUpperCase()}`);
  }

  async getSentimentAnalysis(symbol: string): Promise<ApiResponse> {
    return this.request(`/api/v1/sentiment/${symbol.toUpperCase()}`);
  }

  async getPerformanceMetrics(): Promise<ApiResponse> {
    return this.request('/api/v1/lee_method/stats');
  }

  async getSystemStatus(): Promise<ApiResponse<SystemStatus>> {
    return this.request('/api/v1/status');
  }

  async analyzeChartImage(imageData: string): Promise<ApiResponse> {
    return this.request('/api/v1/multimodal/image/analyze', {
      method: 'POST',
      body: JSON.stringify({ image_data: imageData })
    });
  }

  async getGrokStatus(): Promise<ApiResponse> {
    return this.request('/api/v1/ai/status');
  }

  async getWebSearchResults(query: string): Promise<ApiResponse> {
    return this.request('/api/v1/web_search', {
      method: 'POST',
      body: JSON.stringify({ query })
    });
  }

  // WebSocket Connections - Preserve real-time functionality
  
  connectToScanner(onMessage: (data: any) => void, onError?: (error: Event) => void): WebSocket {
    const wsUrl = this.baseUrl.replace('http', 'ws') + '/ws/scanner';
    const ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
      console.log('🔌 Scanner WebSocket connected');
      // Send initial subscription
      ws.send(JSON.stringify({
        type: 'subscribe_alerts',
        alert_types: ['all']
      }));
    };
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        onMessage(data);
      } catch (error) {
        console.error('❌ Error parsing WebSocket message:', error);
      }
    };
    
    ws.onerror = (error) => {
      console.error('❌ Scanner WebSocket error:', error);
      onError?.(error);
    };
    
    ws.onclose = () => {
      console.log('🔌 Scanner WebSocket disconnected');
      // Auto-reconnect after 3 seconds
      setTimeout(() => {
        this.connectToScanner(onMessage, onError);
      }, 3000);
    };
    
    this.wsConnections.set('scanner', ws);
    return ws;
  }

  connectToProgressUpdates(sessionId: string, onMessage: (data: any) => void): WebSocket {
    const wsUrl = this.baseUrl.replace('http', 'ws') + `/ws/${sessionId}`;
    const ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
      console.log('🔌 Progress WebSocket connected');
    };
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        onMessage(data);
      } catch (error) {
        console.error('❌ Error parsing progress message:', error);
      }
    };
    
    ws.onerror = (error) => {
      console.error('❌ Progress WebSocket error:', error);
    };
    
    ws.onclose = () => {
      console.log('🔌 Progress WebSocket disconnected');
    };
    
    this.wsConnections.set(`progress_${sessionId}`, ws);
    return ws;
  }

  // Utility Methods
  
  private async request(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    };
    
    const config = { ...defaultOptions, ...options };
    
    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`❌ API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  disconnectWebSocket(connectionId: string): void {
    const ws = this.wsConnections.get(connectionId);
    if (ws) {
      ws.close();
      this.wsConnections.delete(connectionId);
    }
  }

  disconnectAllWebSockets(): void {
    this.wsConnections.forEach((ws) => {
      ws.close();
    });
    this.wsConnections.clear();
  }
}

// Export singleton instance
export const atlasApi = new AtlasApiService();
export default atlasApi;
