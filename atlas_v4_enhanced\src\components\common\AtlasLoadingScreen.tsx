import React, { useState, useEffect } from 'react';
import { Box, Typography, LinearProgress, Al<PERSON>, Chip } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';

// Cyberpunk loading animations
const glowPulse = keyframes`
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(0, 255, 136, 0.6);
  }
`;

const textGlow = keyframes`
  0%, 100% {
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.8);
  }
`;

const LoadingContainer = styled(Box)(() => ({
  height: '100vh',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  background: 'linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 100%)',
  position: 'relative',
  overflow: 'hidden',
}));

const AtlasLogo = styled(Typography)(({ theme }) => ({
  fontSize: '4rem',
  fontWeight: 700,
  background: 'linear-gradient(135deg, #00ff88 0%, #0099ff 100%)',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  animation: `${textGlow} 2s ease-in-out infinite`,
  marginBottom: theme.spacing(2),
}));

const LoadingProgress = styled(LinearProgress)(() => ({
  width: '400px',
  height: '8px',
  borderRadius: '4px',
  backgroundColor: 'rgba(255, 255, 255, 0.1)',
  animation: `${glowPulse} 2s ease-in-out infinite`,
  '& .MuiLinearProgress-bar': {
    background: 'linear-gradient(90deg, #00ff88 0%, #0099ff 100%)',
    borderRadius: '4px',
  },
}));

const StatusChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0.5),
  backgroundColor: 'rgba(0, 255, 136, 0.1)',
  color: '#00ff88',
  border: '1px solid rgba(0, 255, 136, 0.3)',
  '&.error': {
    backgroundColor: 'rgba(255, 51, 102, 0.1)',
    color: '#ff3366',
    border: '1px solid rgba(255, 51, 102, 0.3)',
  },
}));

interface AtlasLoadingScreenProps {
  error?: string | null;
  systemStatus?: any;
}

const AtlasLoadingScreen: React.FC<AtlasLoadingScreenProps> = ({ 
  error, 
  systemStatus 
}) => {
  const [progress, setProgress] = useState(0);
  const [currentStage, setCurrentStage] = useState('Initializing A.T.L.A.S. System...');
  const [loadingStages] = useState([
    'Initializing A.T.L.A.S. System...',
    'Connecting to Backend Services...',
    'Loading Trading Engines...',
    'Initializing Lee Method Scanner...',
    'Connecting to Market Data...',
    'Loading Grok AI Integration...',
    'Establishing WebSocket Connections...',
    'Validating System Health...',
    'Loading User Interface...',
    'System Ready!'
  ]);

  useEffect(() => {
    if (error) return;

    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + (100 / loadingStages.length);
        const stageIndex = Math.floor(newProgress / (100 / loadingStages.length));
        
        if (stageIndex < loadingStages.length) {
          setCurrentStage(loadingStages[stageIndex]);
        }
        
        return Math.min(newProgress, 100);
      });
    }, 800);

    return () => clearInterval(interval);
  }, [error, loadingStages]);

  return (
    <LoadingContainer>
      {/* Cyberpunk Background Grid */}
      <div className="atlas-bg-grid" />
      
      <Box
        sx={{
          textAlign: 'center',
          zIndex: 1,
          maxWidth: '600px',
          padding: 4,
        }}
      >
        {/* A.T.L.A.S. Logo */}
        <AtlasLogo variant="h1">
          A.T.L.A.S.
        </AtlasLogo>
        
        <Typography
          variant="h5"
          sx={{
            color: '#8892b0',
            marginBottom: 4,
            fontWeight: 300,
          }}
        >
          Advanced Trading & Learning Analytics System v5.0
        </Typography>

        {/* Loading Progress */}
        {!error && (
          <Box sx={{ marginBottom: 4 }}>
            <LoadingProgress 
              variant="determinate" 
              value={progress} 
            />
            <Typography
              variant="body1"
              sx={{
                color: '#00ff88',
                marginTop: 2,
                fontFamily: 'monospace',
              }}
            >
              {currentStage}
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: '#8892b0',
                marginTop: 1,
              }}
            >
              {Math.round(progress)}% Complete
            </Typography>
          </Box>
        )}

        {/* Error Display */}
        {error && (
          <Alert 
            severity="error" 
            sx={{ 
              marginBottom: 4,
              backgroundColor: 'rgba(255, 51, 102, 0.1)',
              border: '1px solid rgba(255, 51, 102, 0.3)',
              color: '#ff3366',
            }}
          >
            <Typography variant="h6" gutterBottom>
              Initialization Error
            </Typography>
            <Typography variant="body2">
              {error}
            </Typography>
            <Typography variant="body2" sx={{ marginTop: 1, opacity: 0.8 }}>
              System will continue in degraded mode...
            </Typography>
          </Alert>
        )}

        {/* System Status Indicators */}
        {systemStatus && (
          <Box sx={{ marginTop: 4 }}>
            <Typography
              variant="h6"
              sx={{
                color: '#00ff88',
                marginBottom: 2,
              }}
            >
              System Status
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center' }}>
              <StatusChip
                label={`Backend: ${systemStatus.backend_health || 'Unknown'}`}
                className={systemStatus.backend_health === 'healthy' ? '' : 'error'}
              />
              <StatusChip
                label={`Database: ${systemStatus.database_status || 'Unknown'}`}
                className={systemStatus.database_status === 'connected' ? '' : 'error'}
              />
              <StatusChip
                label={`Scanner: ${systemStatus.scanner_status || 'Unknown'}`}
                className={systemStatus.scanner_status === 'active' ? '' : 'error'}
              />
              <StatusChip
                label={`Grok AI: ${systemStatus.grok_status || 'Unknown'}`}
                className={systemStatus.grok_status === 'available' ? '' : 'error'}
              />
            </Box>
          </Box>
        )}

        {/* Feature Highlights */}
        <Box sx={{ marginTop: 4 }}>
          <Typography
            variant="body2"
            sx={{
              color: '#8892b0',
              lineHeight: 1.6,
            }}
          >
            🚀 Grok AI Enhanced • 📊 Real-time Scanner • 💬 Conversational Interface
            <br />
            🎯 35%+ Returns • ⚡ Ultra-responsive Alerts • 🛡️ 100% Backend Reliability
          </Typography>
        </Box>
      </Box>
    </LoadingContainer>
  );
};

export default AtlasLoadingScreen;
