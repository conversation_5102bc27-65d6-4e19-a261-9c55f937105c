import { useState, useEffect, useCallback, useRef } from 'react';
import { atlasApi } from '../services/atlasApi';
import { LeeMethodSignal, WebSocketMessage, ProgressUpdate } from '../types/atlas';

interface AtlasWebSocketState {
  scannerData: LeeMethodSignal[];
  progressUpdates: ProgressUpdate[];
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  lastUpdate: Date | null;
  reconnectAttempts: number;
}

export const useAtlasWebSocket = () => {
  const [state, setState] = useState<AtlasWebSocketState>({
    scannerData: [],
    progressUpdates: [],
    isConnected: false,
    connectionStatus: 'disconnected',
    lastUpdate: null,
    reconnectAttempts: 0,
  });

  const scannerWsRef = useRef<WebSocket | null>(null);
  const progressWsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle scanner WebSocket messages
  const handleScannerMessage = useCallback((data: WebSocketMessage) => {
    console.log('📨 Scanner WebSocket message:', data);

    switch (data.type) {
      case 'scanner_update':
        if (data.data && data.data.pattern_found) {
          const signal: LeeMethodSignal = {
            symbol: data.data.symbol,
            price: data.data.price,
            confidence: data.data.confidence,
            pattern_type: data.data.pattern_type || 'Lee Method',
            timestamp: data.timestamp,
            criteria_met: data.data.criteria_met || {
              trend_confirmation: true,
              volume_validation: true,
              technical_pattern: true,
            },
            strength: data.data.strength || 'MEDIUM',
          };

          setState(prev => ({
            ...prev,
            scannerData: [signal, ...prev.scannerData.slice(0, 19)], // Keep latest 20
            lastUpdate: new Date(),
          }));
        }
        break;

      case 'ping':
        // Respond to ping with pong
        if (scannerWsRef.current?.readyState === WebSocket.OPEN) {
          scannerWsRef.current.send(JSON.stringify({ type: 'pong' }));
        }
        break;

      case 'system_status':
        console.log('📊 System status update:', data.data);
        break;

      default:
        console.log('📨 Unknown scanner message type:', data.type);
    }
  }, []);

  // Handle progress WebSocket messages
  const handleProgressMessage = useCallback((data: WebSocketMessage) => {
    console.log('📈 Progress WebSocket message:', data);

    if (data.type === 'progress_update') {
      const update: ProgressUpdate = {
        stage: data.data.stage,
        percentage: data.data.percentage,
        message: data.data.message,
        timestamp: data.timestamp,
        status: data.data.status,
      };

      setState(prev => ({
        ...prev,
        progressUpdates: [update, ...prev.progressUpdates.slice(0, 9)], // Keep latest 10
        lastUpdate: new Date(),
      }));
    }
  }, []);

  // Connect to scanner WebSocket
  const connectScanner = useCallback(() => {
    if (scannerWsRef.current?.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    setState(prev => ({ ...prev, connectionStatus: 'connecting' }));

    try {
      const ws = atlasApi.connectToScanner(
        handleScannerMessage,
        (error) => {
          console.error('❌ Scanner WebSocket error:', error);
          setState(prev => ({
            ...prev,
            connectionStatus: 'error',
            isConnected: false
          }));
        }
      );

      // Don't override the handlers set by atlasApi, just store the reference
      scannerWsRef.current = ws;

      // Set up our state management by listening to the WebSocket events
      const originalOnOpen = ws.onopen;
      const originalOnClose = ws.onclose;

      ws.onopen = (event) => {
        console.log('✅ Scanner WebSocket connected in hook');
        setState(prev => ({
          ...prev,
          connectionStatus: 'connected',
          isConnected: true,
          reconnectAttempts: 0,
        }));
        if (originalOnOpen) originalOnOpen.call(ws, event);
      };

      ws.onclose = (event) => {
        console.log('🔌 Scanner WebSocket disconnected in hook');
        setState(prev => ({
          ...prev,
          connectionStatus: 'disconnected',
          isConnected: false,
        }));

        if (originalOnClose) originalOnClose.call(ws, event);

        // Auto-reconnect with exponential backoff (only if not a clean close)
        if (event.code !== 1000) {
          const delay = Math.min(1000 * Math.pow(2, state.reconnectAttempts), 30000);
          reconnectTimeoutRef.current = setTimeout(() => {
            setState(prev => ({ ...prev, reconnectAttempts: prev.reconnectAttempts + 1 }));
            connectScanner();
          }, delay);
        }
      };

    } catch (error) {
      console.error('❌ Failed to connect scanner WebSocket:', error);
      setState(prev => ({
        ...prev,
        connectionStatus: 'error',
        isConnected: false
      }));
    }
  }, [handleScannerMessage, state.reconnectAttempts]);

  // Connect to progress WebSocket
  const connectProgress = useCallback((sessionId: string) => {
    try {
      const ws = atlasApi.connectToProgressUpdates(sessionId, handleProgressMessage);
      progressWsRef.current = ws;
    } catch (error) {
      console.error('❌ Failed to connect progress WebSocket:', error);
    }
  }, [handleProgressMessage]);

  // Initialize WebSocket connections
  useEffect(() => {
    connectScanner();

    // Cleanup on unmount
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      
      if (scannerWsRef.current) {
        scannerWsRef.current.close();
      }
      
      if (progressWsRef.current) {
        progressWsRef.current.close();
      }
      
      atlasApi.disconnectAllWebSockets();
    };
  }, [connectScanner]);

  // Manual reconnect function
  const reconnect = useCallback(() => {
    setState(prev => ({ ...prev, reconnectAttempts: 0 }));
    connectScanner();
  }, [connectScanner]);

  // Send message to scanner WebSocket
  const sendScannerMessage = useCallback((message: any) => {
    if (scannerWsRef.current?.readyState === WebSocket.OPEN) {
      scannerWsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('⚠️ Scanner WebSocket not connected');
    }
  }, []);

  return {
    ...state,
    connectProgress,
    reconnect,
    sendScannerMessage,
  };
};
