import { useState, useEffect, useCallback, useRef, createContext, useContext } from 'react';
import { atlasApi } from '../services/atlasApi';
import { LeeMethodSignal, WebSocketMessage, ProgressUpdate } from '../types/atlas';

interface AtlasWebSocketState {
  scannerData: LeeMethodSignal[];
  progressUpdates: ProgressUpdate[];
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  lastUpdate: Date | null;
  reconnectAttempts: number;
}

interface AtlasWebSocketContextType extends AtlasWebSocketState {
  connectProgress: (sessionId: string) => void;
  reconnect: () => void;
  sendScannerMessage: (message: any) => void;
}

// Create WebSocket Context
const AtlasWebSocketContext = createContext<AtlasWebSocketContextType | null>(null);

// Singleton WebSocket Manager
class AtlasWebSocketManager {
  private static instance: AtlasWebSocketManager | null = null;
  private scannerWs: WebSocket | null = null;
  private progressWs: WebSocket | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private listeners: Set<(state: AtlasWebSocketState) => void> = new Set();
  private state: AtlasWebSocketState = {
    scannerData: [],
    progressUpdates: [],
    isConnected: false,
    connectionStatus: 'disconnected',
    lastUpdate: null,
    reconnectAttempts: 0,
  };

  static getInstance(): AtlasWebSocketManager {
    if (!AtlasWebSocketManager.instance) {
      AtlasWebSocketManager.instance = new AtlasWebSocketManager();
    }
    return AtlasWebSocketManager.instance;
  }

  addListener(listener: (state: AtlasWebSocketState) => void) {
    this.listeners.add(listener);
    // Immediately send current state to new listener
    listener(this.state);
  }

  removeListener(listener: (state: AtlasWebSocketState) => void) {
    this.listeners.delete(listener);
  }

  private updateState(updates: Partial<AtlasWebSocketState>) {
    this.state = { ...this.state, ...updates };
    this.listeners.forEach(listener => listener(this.state));
  }

  private handleScannerMessage = (data: WebSocketMessage) => {
    console.log('📨 Scanner WebSocket message:', data);

    switch (data.type) {
      case 'scanner_update':
        if (data.data && data.data.pattern_found) {
          const signal: LeeMethodSignal = {
            symbol: data.data.symbol,
            price: data.data.price,
            confidence: data.data.confidence,
            pattern_type: data.data.pattern_type || 'Lee Method',
            timestamp: data.timestamp,
            criteria_met: data.data.criteria_met || {
              trend_confirmation: true,
              volume_validation: true,
              technical_pattern: true,
            },
            strength: data.data.strength || 'MEDIUM',
          };

          this.updateState({
            scannerData: [signal, ...this.state.scannerData.slice(0, 49)], // Keep latest 50
            lastUpdate: new Date(),
          });
        }
        break;

      case 'connection_established':
        console.log('🔗 Scanner connection established:', data.message);
        this.updateState({
          connectionStatus: 'connected',
          isConnected: true,
          reconnectAttempts: 0,
        });
        break;

      case 'error':
        console.error('❌ Scanner WebSocket error:', data.message);
        this.updateState({
          connectionStatus: 'error',
          isConnected: false,
        });
        break;
    }
  };

  private handleProgressMessage = (data: WebSocketMessage) => {
    console.log('📈 Progress WebSocket message:', data);

    if (data.type === 'progress_update') {
      const update: ProgressUpdate = {
        stage: data.data.stage,
        percentage: data.data.percentage,
        message: data.data.message,
        timestamp: data.timestamp,
        status: data.data.status,
      };

      this.updateState({
        progressUpdates: [update, ...this.state.progressUpdates.slice(0, 9)], // Keep latest 10
        lastUpdate: new Date(),
      });
    }
  };

  connectScanner() {
    // Prevent multiple connections
    if (this.scannerWs?.readyState === WebSocket.OPEN) {
      console.log('🔗 Scanner WebSocket already connected');
      return;
    }

    // Close existing connection if any
    if (this.scannerWs) {
      this.scannerWs.close();
    }

    this.updateState({ connectionStatus: 'connecting' });

    try {
      console.log('🔌 Connecting to Scanner WebSocket...');
      this.scannerWs = atlasApi.connectToScanner(
        this.handleScannerMessage,
        (error) => {
          console.error('❌ Scanner WebSocket error:', error);
          this.updateState({
            connectionStatus: 'error',
            isConnected: false
          });
        }
      );

      this.scannerWs.onopen = () => {
        console.log('✅ Scanner WebSocket connected successfully');
        this.updateState({
          connectionStatus: 'connected',
          isConnected: true,
          reconnectAttempts: 0,
        });
      };

      this.scannerWs.onclose = (event) => {
        console.log('🔌 Scanner WebSocket disconnected', event.code, event.reason);
        this.updateState({
          connectionStatus: 'disconnected',
          isConnected: false,
        });

        // Only auto-reconnect if not a clean close and we have listeners
        if (event.code !== 1000 && this.listeners.size > 0) {
          const delay = Math.min(1000 * Math.pow(2, this.state.reconnectAttempts), 30000);
          console.log(`🔄 Reconnecting in ${delay}ms...`);

          this.reconnectTimeout = setTimeout(() => {
            this.updateState({ reconnectAttempts: this.state.reconnectAttempts + 1 });
            this.connectScanner();
          }, delay);
        }
      };

    } catch (error) {
      console.error('❌ Failed to connect scanner WebSocket:', error);
      this.updateState({
        connectionStatus: 'error',
        isConnected: false
      });
    }
  }

  connectProgress(sessionId: string) {
    try {
      if (this.progressWs) {
        this.progressWs.close();
      }
      this.progressWs = atlasApi.connectToProgressUpdates(sessionId, this.handleProgressMessage);
    } catch (error) {
      console.error('❌ Failed to connect progress WebSocket:', error);
    }
  }

  reconnect() {
    this.updateState({ reconnectAttempts: 0 });
    this.connectScanner();
  }

  sendScannerMessage(message: any) {
    if (this.scannerWs?.readyState === WebSocket.OPEN) {
      this.scannerWs.send(JSON.stringify(message));
    } else {
      console.warn('⚠️ Scanner WebSocket not connected');
    }
  }

  disconnect() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.scannerWs) {
      this.scannerWs.close(1000, 'Manual disconnect');
      this.scannerWs = null;
    }

    if (this.progressWs) {
      this.progressWs.close(1000, 'Manual disconnect');
      this.progressWs = null;
    }

    this.updateState({
      connectionStatus: 'disconnected',
      isConnected: false,
    });
  }
}

// Hook to use the singleton WebSocket manager
export const useAtlasWebSocket = () => {
  const [state, setState] = useState<AtlasWebSocketState>({
    scannerData: [],
    progressUpdates: [],
    isConnected: false,
    connectionStatus: 'disconnected',
    lastUpdate: null,
    reconnectAttempts: 0,
  });

  const managerRef = useRef<AtlasWebSocketManager | null>(null);

  useEffect(() => {
    // Get singleton instance
    managerRef.current = AtlasWebSocketManager.getInstance();

    // Add listener for state updates
    managerRef.current.addListener(setState);

    // Connect scanner if not already connected
    if (!managerRef.current['state'].isConnected) {
      managerRef.current.connectScanner();
    }

    // Cleanup on unmount
    return () => {
      if (managerRef.current) {
        managerRef.current.removeListener(setState);

        // Only disconnect if no other listeners
        if (managerRef.current['listeners'].size === 0) {
          managerRef.current.disconnect();
        }
      }
    };
  }, []);

  const connectProgress = useCallback((sessionId: string) => {
    managerRef.current?.connectProgress(sessionId);
  }, []);

  const reconnect = useCallback(() => {
    managerRef.current?.reconnect();
  }, []);

  const sendScannerMessage = useCallback((message: any) => {
    managerRef.current?.sendScannerMessage(message);
  }, []);

  return {
    ...state,
    connectProgress,
    reconnect,
    sendScannerMessage,
  };
};



