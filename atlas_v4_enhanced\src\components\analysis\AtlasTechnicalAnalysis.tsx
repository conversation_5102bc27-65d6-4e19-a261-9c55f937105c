import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Chip,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,

  Card,
  CardContent,

} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Analytics as AnalyticsIcon,

} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Types
interface TechnicalIndicator {
  name: string;
  value: number;
  signal: 'buy' | 'sell' | 'hold';
  strength: number; // 0-100
  description: string;
}

interface PatternAnalysis {
  pattern: string;
  confidence: number;
  timeframe: string;
  description: string;
  signal: 'bullish' | 'bearish' | 'neutral';
}

interface SupportResistance {
  type: 'support' | 'resistance';
  level: number;
  strength: number;
  touches: number;
}

interface AtlasTechnicalAnalysisProps {
  symbol: string;
  currentPrice: number;
  indicators: TechnicalIndicator[];
  patterns: PatternAnalysis[];
  supportResistance: SupportResistance[];
  overallSignal: 'buy' | 'sell' | 'hold';
  confidence: number;
}

// Styled Components
const AnalysisContainer = styled(Paper)(({ theme }) => ({
  background: 'var(--atlas-gradient-card)',
  border: '1px solid var(--atlas-border-primary)',
  borderRadius: '12px',
  padding: theme.spacing(2),
}));

const SignalCard = styled(Card)<{ signal: string }>(({ signal }) => ({
  background: 
    signal === 'buy' ? 'rgba(0, 255, 136, 0.1)' :
    signal === 'sell' ? 'rgba(255, 82, 82, 0.1)' :
    'rgba(255, 170, 0, 0.1)',
  border: 
    signal === 'buy' ? '1px solid rgba(0, 255, 136, 0.3)' :
    signal === 'sell' ? '1px solid rgba(255, 82, 82, 0.3)' :
    '1px solid rgba(255, 170, 0, 0.3)',
  borderRadius: '8px',
}));

const IndicatorItem = styled(ListItem)<{ signal: string }>(({ signal }) => ({
  borderRadius: '6px',
  marginBottom: '4px',
  backgroundColor: 
    signal === 'buy' ? 'rgba(0, 255, 136, 0.05)' :
    signal === 'sell' ? 'rgba(255, 82, 82, 0.05)' :
    'rgba(255, 170, 0, 0.05)',
}));

const AtlasTechnicalAnalysis: React.FC<AtlasTechnicalAnalysisProps> = ({
  symbol,
  currentPrice,
  indicators,
  patterns,
  supportResistance,
  overallSignal,
  confidence,
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['overview', 'indicators'])
  );

  // Toggle section expansion
  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  // Get signal icon
  const getSignalIcon = (signal: string) => {
    switch (signal) {
      case 'buy':
      case 'bullish':
        return <TrendingUpIcon sx={{ color: '#00ff88' }} />;
      case 'sell':
      case 'bearish':
        return <TrendingDownIcon sx={{ color: '#ff5252' }} />;
      default:
        return <TrendingFlatIcon sx={{ color: '#ffaa00' }} />;
    }
  };

  // Get signal color
  const getSignalColor = (signal: string) => {
    switch (signal) {
      case 'buy':
      case 'bullish':
        return '#00ff88';
      case 'sell':
      case 'bearish':
        return '#ff5252';
      default:
        return '#ffaa00';
    }
  };

  // Calculate indicator summary
  const buySignals = indicators.filter(i => i.signal === 'buy').length;
  const sellSignals = indicators.filter(i => i.signal === 'sell').length;
  const holdSignals = indicators.filter(i => i.signal === 'hold').length;

  return (
    <AnalysisContainer elevation={2}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AnalyticsIcon color="primary" />
          <Typography variant="h6" color="primary">
            Technical Analysis
          </Typography>
          <Chip label={symbol} size="small" color="primary" />
        </Box>
        
        <Typography variant="body2" color="textSecondary">
          ${currentPrice.toFixed(2)}
        </Typography>
      </Box>

      {/* Overall Signal */}
      <Accordion 
        expanded={expandedSections.has('overview')} 
        onChange={() => toggleSection('overview')}
        sx={{ marginBottom: 2, backgroundColor: 'transparent' }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1" color="primary">
            Overall Signal
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <SignalCard signal={overallSignal}>
            <CardContent sx={{ padding: '16px !important' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {getSignalIcon(overallSignal)}
                  <Typography variant="h5" sx={{ color: getSignalColor(overallSignal), fontWeight: 600 }}>
                    {overallSignal.toUpperCase()}
                  </Typography>
                </Box>
                <Chip
                  label={`${confidence}% Confidence`}
                  color={confidence > 70 ? 'success' : confidence > 50 ? 'warning' : 'error'}
                />
              </Box>
              
              <LinearProgress
                variant="determinate"
                value={confidence}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                    backgroundColor: getSignalColor(overallSignal),
                  },
                }}
              />
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', marginTop: 2 }}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="success.main">{buySignals}</Typography>
                  <Typography variant="caption" color="textSecondary">Buy Signals</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="warning.main">{holdSignals}</Typography>
                  <Typography variant="caption" color="textSecondary">Hold Signals</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="error.main">{sellSignals}</Typography>
                  <Typography variant="caption" color="textSecondary">Sell Signals</Typography>
                </Box>
              </Box>
            </CardContent>
          </SignalCard>
        </AccordionDetails>
      </Accordion>

      {/* Technical Indicators */}
      <Accordion 
        expanded={expandedSections.has('indicators')} 
        onChange={() => toggleSection('indicators')}
        sx={{ marginBottom: 2, backgroundColor: 'transparent' }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1" color="primary">
            Technical Indicators ({indicators.length})
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <List dense>
            {indicators.map((indicator, index) => (
              <IndicatorItem key={index} signal={indicator.signal}>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  {getSignalIcon(indicator.signal)}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Typography variant="body2" fontWeight={500}>
                        {indicator.name}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" color="white">
                          {indicator.value.toFixed(2)}
                        </Typography>
                        <Chip
                          label={indicator.signal.toUpperCase()}
                          size="small"
                          sx={{
                            fontSize: '0.6rem',
                            height: 18,
                            backgroundColor: getSignalColor(indicator.signal),
                            color: 'black',
                          }}
                        />
                      </Box>
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="caption" color="textSecondary">
                        {indicator.description}
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={indicator.strength}
                        sx={{
                          marginTop: 0.5,
                          height: 3,
                          borderRadius: 2,
                          backgroundColor: 'rgba(255, 255, 255, 0.1)',
                        }}
                      />
                    </Box>
                  }
                />
              </IndicatorItem>
            ))}
          </List>
        </AccordionDetails>
      </Accordion>

      {/* Pattern Analysis */}
      <Accordion 
        expanded={expandedSections.has('patterns')} 
        onChange={() => toggleSection('patterns')}
        sx={{ marginBottom: 2, backgroundColor: 'transparent' }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1" color="primary">
            Pattern Analysis ({patterns.length})
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 2 }}>
            {patterns.map((pattern, index) => (
              <Card key={index} sx={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}>
                <CardContent sx={{ padding: '12px !important' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 1 }}>
                    <Typography variant="subtitle2" fontWeight={600}>
                      {pattern.pattern}
                    </Typography>
                    <Chip
                      icon={getSignalIcon(pattern.signal)}
                      label={pattern.signal.toUpperCase()}
                      size="small"
                      sx={{
                        fontSize: '0.6rem',
                        height: 18,
                        backgroundColor: getSignalColor(pattern.signal),
                        color: 'black',
                      }}
                    />
                  </Box>

                  <Typography variant="caption" color="textSecondary" display="block" gutterBottom>
                    {pattern.description}
                  </Typography>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="caption" color="textSecondary">
                      {pattern.timeframe}
                    </Typography>
                    <Typography variant="caption" color="primary">
                      {pattern.confidence}% confidence
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            ))}
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* Support & Resistance */}
      <Accordion 
        expanded={expandedSections.has('levels')} 
        onChange={() => toggleSection('levels')}
        sx={{ backgroundColor: 'transparent' }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1" color="primary">
            Support & Resistance ({supportResistance.length})
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <List dense>
            {supportResistance.map((level, index) => (
              <ListItem key={index} sx={{ borderRadius: '6px', marginBottom: '4px' }}>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  {level.type === 'support' ? 
                    <TrendingUpIcon sx={{ color: '#00ff88' }} /> : 
                    <TrendingDownIcon sx={{ color: '#ff5252' }} />
                  }
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Typography variant="body2" fontWeight={500}>
                        {level.type.charAt(0).toUpperCase() + level.type.slice(1)}
                      </Typography>
                      <Typography variant="body2" color="white">
                        ${level.level.toFixed(2)}
                      </Typography>
                    </Box>
                  }
                  secondary={
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="caption" color="textSecondary">
                        {level.touches} touches
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={level.strength}
                        sx={{
                          width: 60,
                          height: 3,
                          borderRadius: 2,
                          backgroundColor: 'rgba(255, 255, 255, 0.1)',
                        }}
                      />
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </AccordionDetails>
      </Accordion>
    </AnalysisContainer>
  );
};

export default AtlasTechnicalAnalysis;
