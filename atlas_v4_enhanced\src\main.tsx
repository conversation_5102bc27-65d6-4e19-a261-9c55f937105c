import React from 'react';
import ReactDOM from 'react-dom/client';
import { useCreateChatClient } from 'stream-chat-react';

import AtlasApp from './components/AtlasApp';
import './styles/cyberpunk-theme.css';

// A.T.L.A.S. Stream Chat Configuration
const STREAM_API_KEY = import.meta.env.VITE_STREAM_API_KEY || 'demo-api-key';
const STREAM_USER_ID = import.meta.env.VITE_STREAM_USER_ID || 'atlas-user-1';
const STREAM_USER_NAME = import.meta.env.VITE_STREAM_USER_NAME || 'A.T.L.A.S. Trader';
const STREAM_USER_TOKEN = import.meta.env.VITE_STREAM_USER_TOKEN || 'demo-token';

// A.T.L.A.S. Main Application Wrapper
const AtlasMainApp: React.FC = () => {
  // Initialize Stream Chat client if credentials are available
  const streamClient = useCreateChatClient({
    apiKey: STREAM_API_KEY,
    tokenOrProvider: STREAM_USER_TOKEN,
    userData: { 
      id: STREAM_USER_ID, 
      name: STREAM_USER_NAME,
      image: `https://getstream.io/random_png/?name=${STREAM_USER_NAME}`,
    },
  });

  console.log('🚀 A.T.L.A.S. v5.0 Enhanced - Modern Chat Interface Starting...');
  console.log('📊 Stream Chat Client:', streamClient ? 'Initialized' : 'Using Fallback Mode');
  console.log('🔧 Environment:', import.meta.env.MODE);

  return (
    <AtlasApp streamChatClient={streamClient || undefined} />
  );
};

// Initialize React Application
const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Root element not found');
}

const root = ReactDOM.createRoot(rootElement);

root.render(
  <React.StrictMode>
    <AtlasMainApp />
  </React.StrictMode>
);

// Development Hot Module Replacement
if (import.meta.hot) {
  import.meta.hot.accept();
}
