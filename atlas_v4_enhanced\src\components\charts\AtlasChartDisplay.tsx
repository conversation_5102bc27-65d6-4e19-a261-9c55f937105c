import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Tooltip,
  Chip,
  Select,
  MenuItem,
  FormControl,

  Switch,
  FormControlLabel,
  ButtonGroup,
  Button,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,

  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,

  Timeline as TimelineIcon,
  BarChart as BarChartIcon,
  CandlestickChart as CandlestickChartIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Types
interface ChartData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface TechnicalIndicator {
  name: string;
  value: number;
  signal: 'buy' | 'sell' | 'hold';
  confidence: number;
}

interface AtlasChartDisplayProps {
  symbol: string;
  data: ChartData[];
  indicators?: TechnicalIndicator[];
  timeframe?: '1m' | '5m' | '15m' | '1h' | '4h' | '1d';
  chartType?: 'candlestick' | 'line' | 'bar';
  showVolume?: boolean;
  showIndicators?: boolean;
  height?: number;
  isFullscreen?: boolean;
  onFullscreenToggle?: () => void;
}

// Styled Components
const ChartContainer = styled(Paper)(() => ({
  background: 'var(--atlas-gradient-card)',
  border: '1px solid var(--atlas-border-primary)',
  borderRadius: '12px',
  overflow: 'hidden',
  position: 'relative',
}));

const ChartHeader = styled(Box)(({ theme }) => ({
  background: 'var(--atlas-bg-secondary)',
  borderBottom: '1px solid var(--atlas-border-primary)',
  padding: theme.spacing(1, 2),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
}));

const ChartCanvas = styled(Box)<{ height: number }>(({ height }) => ({
  height: `${height}px`,
  background: '#0a0a0a',
  position: 'relative',
  overflow: 'hidden',
}));

const IndicatorPanel = styled(Box)(({ theme }) => ({
  background: 'rgba(0, 0, 0, 0.8)',
  border: '1px solid var(--atlas-border-primary)',
  borderRadius: '8px',
  padding: theme.spacing(1),
  position: 'absolute',
  top: theme.spacing(1),
  right: theme.spacing(1),
  minWidth: '200px',
  zIndex: 10,
}));

const AtlasChartDisplay: React.FC<AtlasChartDisplayProps> = ({
  symbol,
  data,
  indicators = [],
  timeframe = '1h',
  chartType = 'candlestick',
  showVolume = true,
  showIndicators = true,
  height = 400,
  isFullscreen = false,
  onFullscreenToggle,
}) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframe);
  const [selectedChartType, setSelectedChartType] = useState(chartType);
  const [showVolumeLocal, setShowVolumeLocal] = useState(showVolume);
  const [showIndicatorsLocal, setShowIndicatorsLocal] = useState(showIndicators);
  const [currentPrice, setCurrentPrice] = useState(0);
  const [priceChange, setPriceChange] = useState(0);
  const [priceChangePercent, setPriceChangePercent] = useState(0);

  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Calculate price metrics
  useEffect(() => {
    if (data.length > 0) {
      const latest = data[data.length - 1];
      const previous = data.length > 1 ? data[data.length - 2] : latest;
      
      setCurrentPrice(latest.close);
      setPriceChange(latest.close - previous.close);
      setPriceChangePercent(((latest.close - previous.close) / previous.close) * 100);
    }
  }, [data]);

  // Simple chart rendering (placeholder for actual charting library)
  useEffect(() => {
    if (!canvasRef.current || data.length === 0) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    // Clear canvas
    ctx.fillStyle = '#0a0a0a';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw grid
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;
    
    // Vertical grid lines
    for (let i = 0; i < 10; i++) {
      const x = (canvas.width / 10) * i;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, canvas.height);
      ctx.stroke();
    }
    
    // Horizontal grid lines
    for (let i = 0; i < 8; i++) {
      const y = (canvas.height / 8) * i;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(canvas.width, y);
      ctx.stroke();
    }

    // Calculate price range
    const prices = data.map(d => [d.high, d.low]).flat();
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;

    // Draw candlesticks or line chart
    if (selectedChartType === 'candlestick') {
      const candleWidth = Math.max(2, canvas.width / data.length - 2);
      
      data.forEach((candle, index) => {
        const x = (canvas.width / data.length) * index + candleWidth / 2;
        const openY = canvas.height - ((candle.open - minPrice) / priceRange) * canvas.height;
        const closeY = canvas.height - ((candle.close - minPrice) / priceRange) * canvas.height;
        const highY = canvas.height - ((candle.high - minPrice) / priceRange) * canvas.height;
        const lowY = canvas.height - ((candle.low - minPrice) / priceRange) * canvas.height;

        // Determine color
        const isGreen = candle.close > candle.open;
        ctx.fillStyle = isGreen ? '#00ff88' : '#ff5252';
        ctx.strokeStyle = isGreen ? '#00ff88' : '#ff5252';

        // Draw wick
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(x, highY);
        ctx.lineTo(x, lowY);
        ctx.stroke();

        // Draw body
        const bodyHeight = Math.abs(closeY - openY);
        const bodyY = Math.min(openY, closeY);
        ctx.fillRect(x - candleWidth / 2, bodyY, candleWidth, bodyHeight);
      });
    } else if (selectedChartType === 'line') {
      ctx.strokeStyle = '#0099ff';
      ctx.lineWidth = 2;
      ctx.beginPath();
      
      data.forEach((point, index) => {
        const x = (canvas.width / data.length) * index;
        const y = canvas.height - ((point.close - minPrice) / priceRange) * canvas.height;
        
        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });
      
      ctx.stroke();
    }

    // Draw volume bars if enabled
    if (showVolumeLocal && data.length > 0) {
      const maxVolume = Math.max(...data.map(d => d.volume));
      const volumeHeight = canvas.height * 0.2; // 20% of chart height
      
      data.forEach((candle, index) => {
        const x = (canvas.width / data.length) * index;
        const barWidth = Math.max(1, canvas.width / data.length - 1);
        const barHeight = (candle.volume / maxVolume) * volumeHeight;
        
        ctx.fillStyle = candle.close > candle.open ? 'rgba(0, 255, 136, 0.3)' : 'rgba(255, 82, 82, 0.3)';
        ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);
      });
    }

  }, [data, selectedChartType, showVolumeLocal]);

  // Get signal color
  const getSignalColor = (signal: string) => {
    switch (signal) {
      case 'buy': return '#00ff88';
      case 'sell': return '#ff5252';
      default: return '#ffaa00';
    }
  };

  return (
    <ChartContainer elevation={2}>
      {/* Header */}
      <ChartHeader>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="h6" color="primary">
            {symbol}
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="h6" color={priceChange >= 0 ? 'success.main' : 'error.main'}>
              ${currentPrice.toFixed(2)}
            </Typography>
            <Chip
              icon={priceChange >= 0 ? <TrendingUpIcon /> : <TrendingDownIcon />}
              label={`${priceChange >= 0 ? '+' : ''}${priceChange.toFixed(2)} (${priceChangePercent.toFixed(2)}%)`}
              size="small"
              color={priceChange >= 0 ? 'success' : 'error'}
            />
          </Box>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* Timeframe Selector */}
          <FormControl size="small" sx={{ minWidth: 80 }}>
            <Select
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value as any)}
              sx={{ color: 'white', fontSize: '0.8rem' }}
            >
              {['1m', '5m', '15m', '1h', '4h', '1d'].map(tf => (
                <MenuItem key={tf} value={tf}>{tf}</MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Chart Type Selector */}
          <ButtonGroup size="small">
            <Button
              variant={selectedChartType === 'candlestick' ? 'contained' : 'outlined'}
              onClick={() => setSelectedChartType('candlestick')}
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <CandlestickChartIcon fontSize="small" />
            </Button>
            <Button
              variant={selectedChartType === 'line' ? 'contained' : 'outlined'}
              onClick={() => setSelectedChartType('line')}
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <TimelineIcon fontSize="small" />
            </Button>
            <Button
              variant={selectedChartType === 'bar' ? 'contained' : 'outlined'}
              onClick={() => setSelectedChartType('bar')}
              sx={{ minWidth: 'auto', padding: '4px 8px' }}
            >
              <BarChartIcon fontSize="small" />
            </Button>
          </ButtonGroup>

          {/* Toggle Controls */}
          <FormControlLabel
            control={
              <Switch
                checked={showVolumeLocal}
                onChange={(e) => setShowVolumeLocal(e.target.checked)}
                size="small"
              />
            }
            label="Volume"
            sx={{ margin: 0, '& .MuiFormControlLabel-label': { fontSize: '0.8rem' } }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={showIndicatorsLocal}
                onChange={(e) => setShowIndicatorsLocal(e.target.checked)}
                size="small"
              />
            }
            label="Indicators"
            sx={{ margin: 0, '& .MuiFormControlLabel-label': { fontSize: '0.8rem' } }}
          />

          {/* Fullscreen Toggle */}
          {onFullscreenToggle && (
            <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
              <IconButton size="small" onClick={onFullscreenToggle}>
                {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </ChartHeader>

      {/* Chart Area */}
      <ChartCanvas height={height}>
        <canvas
          ref={canvasRef}
          style={{
            width: '100%',
            height: '100%',
            display: 'block',
          }}
        />

        {/* Technical Indicators Panel */}
        {showIndicatorsLocal && indicators.length > 0 && (
          <IndicatorPanel>
            <Typography variant="subtitle2" color="primary" gutterBottom>
              Technical Indicators
            </Typography>
            {indicators.map((indicator, index) => (
              <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', marginBottom: 0.5 }}>
                <Typography variant="caption" color="textSecondary">
                  {indicator.name}:
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Typography variant="caption" color="white">
                    {indicator.value.toFixed(2)}
                  </Typography>
                  <Chip
                    label={indicator.signal.toUpperCase()}
                    size="small"
                    sx={{
                      fontSize: '0.6rem',
                      height: 16,
                      backgroundColor: getSignalColor(indicator.signal),
                      color: 'black',
                    }}
                  />
                </Box>
              </Box>
            ))}
          </IndicatorPanel>
        )}
      </ChartCanvas>
    </ChartContainer>
  );
};

export default AtlasChartDisplay;
