import React, { useState, useEffect } from 'react';
import {
  Box,
  LinearProgress,
  Typography,
  Paper,
  Chip,
  Collapse,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
  Schedule as ScheduleIcon,
  Error as ErrorIcon,
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon,
  Search as SearchIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Types
interface ProgressStage {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'active' | 'completed' | 'error';
  progress?: number;
  duration?: number;
  details?: string[];
}

interface AtlasProgressIndicatorProps {
  stages: ProgressStage[];
  currentStage?: string;
  overallProgress?: number;
  isVisible?: boolean;
  title?: string;
  compact?: boolean;
}

// Styled Components
const ProgressContainer = styled(Paper)(({ theme }) => ({
  background: 'var(--atlas-gradient-card)',
  border: '1px solid var(--atlas-border-primary)',
  borderRadius: '12px',
  padding: theme.spacing(2),
  marginBottom: theme.spacing(1),
}));

const StageItem = styled(ListItem)<{ status: string }>(({ theme, status }) => ({
  borderRadius: '8px',
  marginBottom: theme.spacing(0.5),
  backgroundColor: 
    status === 'active' ? 'rgba(0, 153, 255, 0.1)' :
    status === 'completed' ? 'rgba(0, 255, 136, 0.1)' :
    status === 'error' ? 'rgba(255, 82, 82, 0.1)' :
    'transparent',
  border: 
    status === 'active' ? '1px solid rgba(0, 153, 255, 0.3)' :
    status === 'completed' ? '1px solid rgba(0, 255, 136, 0.3)' :
    status === 'error' ? '1px solid rgba(255, 82, 82, 0.3)' :
    '1px solid transparent',
}));

const AtlasProgressIndicator: React.FC<AtlasProgressIndicatorProps> = ({
  stages,
  currentStage,
  overallProgress = 0,
  isVisible = true,
  title = "Processing",
  compact = false,
}) => {
  const [expanded, setExpanded] = useState(!compact);
  const [animatedProgress, setAnimatedProgress] = useState(0);

  // Animate progress bar
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedProgress(overallProgress);
    }, 100);
    return () => clearTimeout(timer);
  }, [overallProgress]);

  // Get stage icon based on status
  const getStageIcon = (stage: ProgressStage) => {
    switch (stage.status) {
      case 'completed':
        return <CheckCircleIcon sx={{ color: '#00ff88' }} />;
      case 'active':
        return <ScheduleIcon sx={{ color: '#0099ff' }} />;
      case 'error':
        return <ErrorIcon sx={{ color: '#ff5252' }} />;
      default:
        return <RadioButtonUncheckedIcon sx={{ color: 'rgba(255, 255, 255, 0.3)' }} />;
    }
  };

  // Get stage-specific icon
  const getStageTypeIcon = (stageName: string) => {
    if (stageName.toLowerCase().includes('grok') || stageName.toLowerCase().includes('ai')) {
      return <PsychologyIcon fontSize="small" />;
    }
    if (stageName.toLowerCase().includes('analysis') || stageName.toLowerCase().includes('trading')) {
      return <TrendingUpIcon fontSize="small" />;
    }
    if (stageName.toLowerCase().includes('search') || stageName.toLowerCase().includes('news')) {
      return <SearchIcon fontSize="small" />;
    }
    if (stageName.toLowerCase().includes('data') || stageName.toLowerCase().includes('processing')) {
      return <AnalyticsIcon fontSize="small" />;
    }
    return null;
  };

  // Calculate completion stats
  const completedStages = stages.filter(s => s.status === 'completed').length;
  const totalStages = stages.length;
  const activeStage = stages.find(s => s.status === 'active');

  if (!isVisible) return null;

  return (
    <ProgressContainer elevation={2}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="h6" color="primary">
            {title}
          </Typography>
          <Chip
            label={`${completedStages}/${totalStages}`}
            size="small"
            color={completedStages === totalStages ? "success" : "primary"}
          />
        </Box>
        
        {!compact && (
          <IconButton
            onClick={() => setExpanded(!expanded)}
            size="small"
            sx={{ color: 'var(--atlas-text-secondary)' }}
          >
            {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        )}
      </Box>

      {/* Overall Progress Bar */}
      <Box sx={{ marginBottom: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', marginBottom: 1 }}>
          <Typography variant="body2" color="textSecondary">
            {activeStage ? activeStage.name : 'Processing...'}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            {Math.round(animatedProgress)}%
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={animatedProgress}
          sx={{
            height: 8,
            borderRadius: 4,
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            '& .MuiLinearProgress-bar': {
              borderRadius: 4,
              background: 'var(--atlas-gradient-primary)',
            },
          }}
        />
      </Box>

      {/* Stage Details */}
      <Collapse in={expanded}>
        <List dense>
          {stages.map((stage, index) => (
            <React.Fragment key={stage.id}>
              <StageItem status={stage.status}>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  {getStageIcon(stage)}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getStageTypeIcon(stage.name)}
                      <Typography variant="body2" fontWeight={stage.status === 'active' ? 600 : 400}>
                        {stage.name}
                      </Typography>
                      {stage.duration && (
                        <Chip
                          label={`${stage.duration}ms`}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.7rem', height: 20 }}
                        />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="caption" color="textSecondary">
                        {stage.description}
                      </Typography>
                      {stage.progress !== undefined && stage.status === 'active' && (
                        <LinearProgress
                          variant="determinate"
                          value={stage.progress}
                          size="small"
                          sx={{
                            marginTop: 0.5,
                            height: 4,
                            borderRadius: 2,
                            backgroundColor: 'rgba(255, 255, 255, 0.1)',
                          }}
                        />
                      )}
                      {stage.details && stage.details.length > 0 && (
                        <Box sx={{ marginTop: 0.5 }}>
                          {stage.details.map((detail, idx) => (
                            <Typography key={idx} variant="caption" display="block" color="textSecondary">
                              • {detail}
                            </Typography>
                          ))}
                        </Box>
                      )}
                    </Box>
                  }
                />
              </StageItem>
              {index < stages.length - 1 && <Divider sx={{ margin: '4px 0' }} />}
            </React.Fragment>
          ))}
        </List>
      </Collapse>
    </ProgressContainer>
  );
};

export default AtlasProgressIndicator;
