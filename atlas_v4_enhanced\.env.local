# A.T.L.A.S. v5.0 Enhanced - Local Development Configuration

# Backend API Configuration
VITE_API_BASE_URL=http://localhost:8001

# Stream Chat Configuration (Demo/Development)
VITE_STREAM_API_KEY=demo-api-key
VITE_STREAM_USER_ID=atlas-user-1
VITE_STREAM_USER_NAME=A.T.L.A.S. Trader
VITE_STREAM_USER_TOKEN=demo-token

# Development Configuration
VITE_NODE_ENV=development
VITE_DEBUG_MODE=true

# Feature Flags
VITE_ENABLE_GROK_AI=true
VITE_ENABLE_REAL_TIME_SCANNER=true
VITE_ENABLE_NEWS_INSIGHTS=true
VITE_ENABLE_VOICE_INPUT=false

# Theme Configuration
VITE_DEFAULT_THEME=cyberpunk
VITE_ENABLE_THEME_SWITCHING=true

# Performance Configuration
VITE_WEBSOCKET_RECONNECT_INTERVAL=3000
VITE_API_TIMEOUT=30000
VITE_CHAT_MESSAGE_LIMIT=100
