/* A.T.L.A.S. Cyberpunk Theme - Futuristic Trading Interface */
:root {
  /* Primary Colors - Cyberpunk Palette */
  --atlas-primary: #00ff88;
  --atlas-primary-dark: #00cc6a;
  --atlas-primary-light: #33ffaa;
  --atlas-secondary: #0099ff;
  --atlas-accent: #ff0080;
  --atlas-warning: #ffaa00;
  --atlas-danger: #ff3366;
  
  /* Background Colors - Dark Cyberpunk */
  --atlas-bg-primary: #0a0a0f;
  --atlas-bg-secondary: #1a1a2e;
  --atlas-bg-tertiary: #16213e;
  --atlas-bg-card: #1e1e3f;
  --atlas-bg-hover: #2a2a4a;
  
  /* Text Colors */
  --atlas-text-primary: #ffffff;
  --atlas-text-secondary: #8892b0;
  --atlas-text-muted: #64748b;
  --atlas-text-accent: #00ff88;
  
  /* Border Colors */
  --atlas-border-primary: #16213e;
  --atlas-border-secondary: #2a2a4a;
  --atlas-border-accent: #00ff88;
  --atlas-border-glow: rgba(0, 255, 136, 0.3);
  
  /* Glow Effects */
  --atlas-glow-primary: 0 0 20px rgba(0, 255, 136, 0.3);
  --atlas-glow-secondary: 0 0 15px rgba(0, 153, 255, 0.3);
  --atlas-glow-accent: 0 0 10px rgba(255, 0, 128, 0.3);
  
  /* Gradients */
  --atlas-gradient-primary: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
  --atlas-gradient-secondary: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  --atlas-gradient-card: linear-gradient(135deg, #1e1e3f 0%, #2a2a4a 100%);
  
  /* Animations */
  --atlas-transition-fast: 0.15s ease-in-out;
  --atlas-transition-normal: 0.3s ease-in-out;
  --atlas-transition-slow: 0.5s ease-in-out;
  
  /* Typography */
  --atlas-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  --atlas-font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  
  /* Spacing */
  --atlas-spacing-xs: 0.25rem;
  --atlas-spacing-sm: 0.5rem;
  --atlas-spacing-md: 1rem;
  --atlas-spacing-lg: 1.5rem;
  --atlas-spacing-xl: 2rem;
  --atlas-spacing-2xl: 3rem;
  
  /* Border Radius */
  --atlas-radius-sm: 4px;
  --atlas-radius-md: 8px;
  --atlas-radius-lg: 12px;
  --atlas-radius-xl: 16px;
  --atlas-radius-full: 9999px;
  
  /* Z-Index Layers */
  --atlas-z-dropdown: 1000;
  --atlas-z-sticky: 1020;
  --atlas-z-fixed: 1030;
  --atlas-z-modal-backdrop: 1040;
  --atlas-z-modal: 1050;
  --atlas-z-popover: 1060;
  --atlas-z-tooltip: 1070;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: var(--atlas-font-family);
  background: var(--atlas-bg-primary);
  color: var(--atlas-text-primary);
  overflow: hidden;
}

#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--atlas-bg-secondary);
  border-radius: var(--atlas-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--atlas-primary);
  border-radius: var(--atlas-radius-sm);
  box-shadow: var(--atlas-glow-primary);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--atlas-primary-light);
}

/* Cyberpunk Glow Effects */
.atlas-glow {
  box-shadow: var(--atlas-glow-primary);
}

.atlas-glow-secondary {
  box-shadow: var(--atlas-glow-secondary);
}

.atlas-glow-accent {
  box-shadow: var(--atlas-glow-accent);
}

/* Animated Background Grid */
.atlas-bg-grid {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0, 255, 136, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 136, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* Pulsing Animation for Active Elements */
.atlas-pulse {
  animation: atlas-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes atlas-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Glitch Effect for Special Elements */
.atlas-glitch {
  position: relative;
  animation: atlas-glitch 2s infinite;
}

.atlas-glitch::before,
.atlas-glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.atlas-glitch::before {
  animation: atlas-glitch-1 0.5s infinite;
  color: var(--atlas-accent);
  z-index: -1;
}

.atlas-glitch::after {
  animation: atlas-glitch-2 0.5s infinite;
  color: var(--atlas-secondary);
  z-index: -2;
}

@keyframes atlas-glitch {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
}

@keyframes atlas-glitch-1 {
  0%, 100% { transform: translate(0); }
  10% { transform: translate(-2px, -2px); }
  20% { transform: translate(2px, 2px); }
  30% { transform: translate(-2px, 2px); }
  40% { transform: translate(2px, -2px); }
  50% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  70% { transform: translate(-2px, 2px); }
  80% { transform: translate(2px, -2px); }
  90% { transform: translate(-2px, -2px); }
}

@keyframes atlas-glitch-2 {
  0%, 100% { transform: translate(0); }
  10% { transform: translate(2px, 2px); }
  20% { transform: translate(-2px, -2px); }
  30% { transform: translate(2px, -2px); }
  40% { transform: translate(-2px, 2px); }
  50% { transform: translate(2px, 2px); }
  60% { transform: translate(-2px, -2px); }
  70% { transform: translate(2px, -2px); }
  80% { transform: translate(-2px, 2px); }
  90% { transform: translate(2px, 2px); }
}

/* Typing Animation */
.atlas-typing {
  overflow: hidden;
  border-right: 2px solid var(--atlas-primary);
  white-space: nowrap;
  animation: 
    atlas-typing 3.5s steps(40, end),
    atlas-blink-caret 0.75s step-end infinite;
}

@keyframes atlas-typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes atlas-blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--atlas-primary); }
}

/* Utility Classes */
.atlas-text-gradient {
  background: var(--atlas-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.atlas-border-glow {
  border: 1px solid var(--atlas-border-accent);
  box-shadow: var(--atlas-border-glow);
}

.atlas-card {
  background: var(--atlas-gradient-card);
  border: 1px solid var(--atlas-border-primary);
  border-radius: var(--atlas-radius-lg);
  backdrop-filter: blur(10px);
}

.atlas-button {
  background: var(--atlas-gradient-primary);
  border: none;
  border-radius: var(--atlas-radius-md);
  color: var(--atlas-bg-primary);
  font-weight: 600;
  padding: var(--atlas-spacing-sm) var(--atlas-spacing-lg);
  cursor: pointer;
  transition: all var(--atlas-transition-fast);
  box-shadow: var(--atlas-glow-primary);
}

.atlas-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 25px rgba(0, 255, 136, 0.5);
}

.atlas-button:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  :root {
    --atlas-spacing-md: 0.75rem;
    --atlas-spacing-lg: 1rem;
    --atlas-spacing-xl: 1.5rem;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --atlas-border-primary: #ffffff;
    --atlas-text-secondary: #ffffff;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
