import React, { useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import Box from '@mui/material/Box';
import { Chat } from 'stream-chat-react';
import { StreamChat as StreamChatClient } from 'stream-chat';

// A.T.L.A.S. Components
import AtlasChatInterface from './chat/AtlasChatInterface';
import AtlasScannerPanel from './scanner/AtlasScannerPanel';
import AtlasStatusPanel from './status/AtlasStatusPanel';
import AtlasHeader from './layout/AtlasHeader';
import AtlasLoadingScreen from './common/AtlasLoadingScreen';

// Services and Hooks
import { atlasApi } from '../services/atlasApi';
import { useAtlasWebSocket } from '../hooks/useAtlasWebSocket';
import { useAtlasSystem } from '../hooks/useAtlasSystem';

// Styles
import '../styles/cyberpunk-theme.css';
import 'stream-chat-react/dist/css/v2/index.css';

// A.T.L.A.S. Cyberpunk Theme Configuration
const atlasCyberpunkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#00ff88',
      dark: '#00cc6a',
      light: '#33ffaa',
    },
    secondary: {
      main: '#0099ff',
      dark: '#0077cc',
      light: '#33aaff',
    },
    error: {
      main: '#ff3366',
    },
    warning: {
      main: '#ffaa00',
    },
    background: {
      default: '#0a0a0f',
      paper: '#1a1a2e',
    },
    text: {
      primary: '#ffffff',
      secondary: '#8892b0',
    },
  },
  typography: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif',
    h1: {
      fontWeight: 700,
      background: 'linear-gradient(135deg, #00ff88 0%, #0099ff 100%)',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
    },
    h2: {
      fontWeight: 600,
      color: '#00ff88',
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          background: 'linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 100%)',
          overflow: 'hidden',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          background: 'linear-gradient(135deg, #1e1e3f 0%, #2a2a4a 100%)',
          border: '1px solid #16213e',
          backdropFilter: 'blur(10px)',
        },
      },
    },
  },
});

interface AtlasAppProps {
  streamChatClient?: StreamChatClient;
}

const AtlasApp: React.FC<AtlasAppProps> = ({ streamChatClient }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);
  const [systemStatus, setSystemStatus] = useState<any>(null);

  // A.T.L.A.S. System Hooks
  const {
    scannerData,
    isConnected: scannerConnected,
    connectionStatus
  } = useAtlasWebSocket();
  
  const {
    healthStatus,
    performanceMetrics,
    isSystemHealthy
  } = useAtlasSystem();

  // Initialize A.T.L.A.S. System
  useEffect(() => {
    const initializeAtlas = async () => {
      try {
        console.log('🚀 Initializing A.T.L.A.S. v5.0 Enhanced System...');

        // Set a maximum timeout for initialization
        const initTimeout = setTimeout(() => {
          console.log('⏰ Initialization timeout - forcing interface load');
          setIsInitialized(true);
        }, 8000); // 8 second timeout
        
        // 1. Check backend health
        const healthResponse = await atlasApi.healthCheck();
        console.log('🏥 Health check response:', healthResponse);
        if (!healthResponse.success) {
          throw new Error(`Backend health check failed: ${healthResponse.error || 'Unknown error'}`);
        }
        
        // 2. Get system status
        const statusResponse = await atlasApi.getSystemStatus();
        console.log('📊 System status response:', statusResponse);
        if (statusResponse.success) {
          setSystemStatus(statusResponse.data);
          console.log('📊 System status set:', statusResponse.data);
        } else {
          console.error('❌ Failed to get system status:', statusResponse.error);
        }

        // 3. Initialize scanner data (with timeout)
        try {
          const scannerResponse = await Promise.race([
            atlasApi.getLeeMethodSignals(),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Scanner initialization timeout')), 5000)
            )
          ]);
          console.log('📊 Scanner response:', scannerResponse);
          if (scannerResponse.success) {
            console.log('📊 Scanner initialized:', scannerResponse.data?.signals?.length || 0, 'signals');
          }
        } catch (scannerError) {
          console.warn('⚠️ Scanner initialization failed, continuing anyway:', scannerError);
          // Continue without scanner data - it will load via WebSocket
        }
        
        // 4. Verify critical endpoints (with individual timeouts)
        try {
          // Test Grok status (should be fast)
          const grokResponse = await Promise.race([
            atlasApi.getGrokStatus(),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Grok status timeout')), 3000)
            )
          ]);
          console.log('✅ Grok status verified:', grokResponse.success);
        } catch (grokError) {
          console.warn('⚠️ Grok status check failed:', grokError);
        }

        try {
          // Test performance metrics (may be slow, so shorter timeout)
          const perfResponse = await Promise.race([
            atlasApi.getPerformanceMetrics(),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Performance metrics timeout')), 2000)
            )
          ]);
          console.log('✅ Performance metrics verified:', perfResponse.success);
        } catch (perfError) {
          console.warn('⚠️ Performance metrics check failed:', perfError);
        }

        console.log('✅ A.T.L.A.S. system fully initialized');
        clearTimeout(initTimeout);
        setIsInitialized(true);
        
      } catch (error) {
        console.error('❌ A.T.L.A.S. initialization failed:', error);
        setInitializationError(error instanceof Error ? error.message : 'Unknown error');

        // Still allow the interface to load in degraded mode
        console.log('⚠️ Loading interface in degraded mode...');
        clearTimeout(initTimeout);
        setTimeout(() => {
          console.log('🔄 Forcing interface load after timeout');
          setIsInitialized(true);
        }, 3000);
      }
    };

    initializeAtlas();
  }, []);

  // Show loading screen during initialization
  if (!isInitialized) {
    return (
      <ThemeProvider theme={atlasCyberpunkTheme}>
        <CssBaseline />
        <AtlasLoadingScreen 
          error={initializationError}
          systemStatus={systemStatus}
        />
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={atlasCyberpunkTheme}>
      <CssBaseline />
      
      {/* Cyberpunk Background Grid */}
      <div className="atlas-bg-grid" />
      
      <Box
        sx={{
          height: '100vh',
          display: 'flex',
          flexDirection: 'column',
          background: 'transparent',
          position: 'relative',
          zIndex: 1,
        }}
      >
        {/* Header */}
        <AtlasHeader 
          systemStatus={systemStatus}
          isConnected={scannerConnected}
          connectionStatus={connectionStatus}
        />
        
        {/* Main Interface */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            overflow: 'hidden',
          }}
        >
          {/* Left Panel - Scanner */}
          <Box
            sx={{
              width: 400,
              borderRight: '1px solid var(--atlas-border-primary)',
              background: 'var(--atlas-bg-secondary)',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <AtlasScannerPanel 
              scannerData={scannerData}
              isConnected={scannerConnected}
            />
          </Box>
          
          {/* Center Panel - Chat Interface */}
          <Box
            sx={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              background: 'var(--atlas-bg-primary)',
            }}
          >
            {streamChatClient ? (
              <Chat client={streamChatClient} theme="str-chat__theme-dark">
                <AtlasChatInterface />
              </Chat>
            ) : (
              <AtlasChatInterface />
            )}
          </Box>
          
          {/* Right Panel - Status & Analytics */}
          <Box
            sx={{
              width: 350,
              borderLeft: '1px solid var(--atlas-border-primary)',
              background: 'var(--atlas-bg-secondary)',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <AtlasStatusPanel 
              systemStatus={systemStatus}
              healthStatus={healthStatus}
              performanceMetrics={performanceMetrics}
              isSystemHealthy={isSystemHealthy}
            />
          </Box>
        </Box>
      </Box>
    </ThemeProvider>
  );
};

export default AtlasApp;
