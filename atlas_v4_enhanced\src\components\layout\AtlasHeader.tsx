import React, { useState } from 'react';
import {
  A<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  Box,
  IconButton,
  Badge,
  Chip,
  Menu,
  MenuItem,
  Tooltip,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  TrendingUp as TrendingUpIcon,
  Speed as SpeedIcon,
  Wifi as WifiIcon,
  WifiOff as WifiOffIcon,
  Psychology as PsychologyIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const StyledAppBar = styled(AppBar)(() => ({
  background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',
  borderBottom: '1px solid var(--atlas-border-primary)',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
  backdropFilter: 'blur(10px)',
}));

const AtlasLogo = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  background: 'linear-gradient(135deg, #00ff88 0%, #0099ff 100%)',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  marginRight: theme.spacing(2),
}));

const StatusIndicator = styled(Box)<{ status: 'connected' | 'disconnected' | 'error' }>(
  ({ theme, status }) => ({
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(1),
    padding: theme.spacing(0.5, 1),
    borderRadius: theme.spacing(1),
    backgroundColor: 
      status === 'connected' 
        ? 'rgba(0, 255, 136, 0.1)' 
        : status === 'error'
        ? 'rgba(255, 51, 102, 0.1)'
        : 'rgba(136, 146, 176, 0.1)',
    border: `1px solid ${
      status === 'connected' 
        ? 'rgba(0, 255, 136, 0.3)' 
        : status === 'error'
        ? 'rgba(255, 51, 102, 0.3)'
        : 'rgba(136, 146, 176, 0.3)'
    }`,
    color: 
      status === 'connected' 
        ? '#00ff88' 
        : status === 'error'
        ? '#ff3366'
        : '#8892b0',
  })
);

interface AtlasHeaderProps {
  systemStatus?: any;
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
}

const AtlasHeader: React.FC<AtlasHeaderProps> = ({
  systemStatus,
  isConnected: _isConnected,
  connectionStatus,
}) => {
  const [settingsAnchor, setSettingsAnchor] = useState<null | HTMLElement>(null);
  const [notificationsAnchor, setNotificationsAnchor] = useState<null | HTMLElement>(null);

  const handleSettingsClick = (event: React.MouseEvent<HTMLElement>) => {
    setSettingsAnchor(event.currentTarget);
  };

  const handleNotificationsClick = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationsAnchor(event.currentTarget);
  };

  const handleClose = () => {
    setSettingsAnchor(null);
    setNotificationsAnchor(null);
  };

  const getConnectionIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <WifiIcon fontSize="small" />;
      case 'connecting':
        return <WifiIcon fontSize="small" className="atlas-pulse" />;
      default:
        return <WifiOffIcon fontSize="small" />;
    }
  };

  const getConnectionStatus = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'connected';
      case 'connecting':
        return 'disconnected';
      case 'error':
        return 'error';
      default:
        return 'disconnected';
    }
  };

  return (
    <StyledAppBar position="static" elevation={0}>
      <Toolbar sx={{ minHeight: '64px !important' }}>
        {/* Logo and Title */}
        <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
          <AtlasLogo variant="h5">
            A.T.L.A.S.
          </AtlasLogo>
          <Typography
            variant="body2"
            sx={{
              color: '#8892b0',
              fontWeight: 300,
            }}
          >
            v5.0 Enhanced • Modern Chat Interface
          </Typography>
        </Box>

        {/* System Status Indicators */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {/* Connection Status */}
          <StatusIndicator status={getConnectionStatus()}>
            {getConnectionIcon()}
            <Typography variant="caption">
              {connectionStatus.charAt(0).toUpperCase() + connectionStatus.slice(1)}
            </Typography>
          </StatusIndicator>

          {/* System Health */}
          {systemStatus && (
            <Tooltip title="System Health">
              <Chip
                icon={<SpeedIcon />}
                label={systemStatus.backend_health || 'Unknown'}
                size="small"
                sx={{
                  backgroundColor: 
                    systemStatus.backend_health === 'healthy' 
                      ? 'rgba(0, 255, 136, 0.1)' 
                      : 'rgba(255, 51, 102, 0.1)',
                  color: 
                    systemStatus.backend_health === 'healthy' 
                      ? '#00ff88' 
                      : '#ff3366',
                  border: `1px solid ${
                    systemStatus.backend_health === 'healthy' 
                      ? 'rgba(0, 255, 136, 0.3)' 
                      : 'rgba(255, 51, 102, 0.3)'
                  }`,
                }}
              />
            </Tooltip>
          )}

          {/* Grok AI Status */}
          {systemStatus?.grok_status && (
            <Tooltip title="Grok AI Integration">
              <Chip
                icon={<PsychologyIcon />}
                label={systemStatus.grok_status}
                size="small"
                sx={{
                  backgroundColor: 
                    systemStatus.grok_status === 'available' 
                      ? 'rgba(0, 153, 255, 0.1)' 
                      : 'rgba(255, 170, 0, 0.1)',
                  color: 
                    systemStatus.grok_status === 'available' 
                      ? '#0099ff' 
                      : '#ffaa00',
                  border: `1px solid ${
                    systemStatus.grok_status === 'available' 
                      ? 'rgba(0, 153, 255, 0.3)' 
                      : 'rgba(255, 170, 0, 0.3)'
                  }`,
                }}
              />
            </Tooltip>
          )}

          {/* Performance Indicator */}
          <Tooltip title="Trading Performance">
            <Chip
              icon={<TrendingUpIcon />}
              label="35%+ Returns"
              size="small"
              sx={{
                backgroundColor: 'rgba(0, 255, 136, 0.1)',
                color: '#00ff88',
                border: '1px solid rgba(0, 255, 136, 0.3)',
              }}
            />
          </Tooltip>

          {/* Notifications */}
          <Tooltip title="Notifications">
            <IconButton
              color="inherit"
              onClick={handleNotificationsClick}
              sx={{ color: '#8892b0' }}
            >
              <Badge badgeContent={3} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>

          {/* Settings */}
          <Tooltip title="Settings">
            <IconButton
              color="inherit"
              onClick={handleSettingsClick}
              sx={{ color: '#8892b0' }}
            >
              <SettingsIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Settings Menu */}
        <Menu
          anchorEl={settingsAnchor}
          open={Boolean(settingsAnchor)}
          onClose={handleClose}
          PaperProps={{
            sx: {
              background: 'var(--atlas-bg-card)',
              border: '1px solid var(--atlas-border-primary)',
              backdropFilter: 'blur(10px)',
            },
          }}
        >
          <MenuItem onClick={handleClose}>System Configuration</MenuItem>
          <MenuItem onClick={handleClose}>Trading Preferences</MenuItem>
          <MenuItem onClick={handleClose}>API Settings</MenuItem>
          <MenuItem onClick={handleClose}>Theme Options</MenuItem>
        </Menu>

        {/* Notifications Menu */}
        <Menu
          anchorEl={notificationsAnchor}
          open={Boolean(notificationsAnchor)}
          onClose={handleClose}
          PaperProps={{
            sx: {
              background: 'var(--atlas-bg-card)',
              border: '1px solid var(--atlas-border-primary)',
              backdropFilter: 'blur(10px)',
              minWidth: '300px',
            },
          }}
        >
          <MenuItem onClick={handleClose}>
            <Box>
              <Typography variant="body2" color="primary">
                New Lee Method Signal
              </Typography>
              <Typography variant="caption" color="textSecondary">
                AAPL - Strong Buy Signal Detected
              </Typography>
            </Box>
          </MenuItem>
          <MenuItem onClick={handleClose}>
            <Box>
              <Typography variant="body2" color="warning.main">
                System Update Available
              </Typography>
              <Typography variant="caption" color="textSecondary">
                A.T.L.A.S. v5.1 with enhanced features
              </Typography>
            </Box>
          </MenuItem>
          <MenuItem onClick={handleClose}>
            <Box>
              <Typography variant="body2" color="info.main">
                Market Alert
              </Typography>
              <Typography variant="caption" color="textSecondary">
                High volatility detected in tech sector
              </Typography>
            </Box>
          </MenuItem>
        </Menu>
      </Toolbar>
    </StyledAppBar>
  );
};

export default AtlasHeader;
