import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  Chip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,

} from '@mui/material';
import {
  ShowChart as ShowChartIcon,
  Analytics as AnalyticsIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Import our chart components
import AtlasChartDisplay from './AtlasChartDisplay';
import AtlasTechnicalAnalysis from '../analysis/AtlasTechnicalAnalysis';

// Types
interface ChartData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface TechnicalIndicator {
  name: string;
  value: number;
  signal: 'buy' | 'sell' | 'hold';
  strength: number;
  description: string;
}

interface PatternAnalysis {
  pattern: string;
  confidence: number;
  timeframe: string;
  description: string;
  signal: 'bullish' | 'bearish' | 'neutral';
}

interface SupportResistance {
  type: 'support' | 'resistance';
  level: number;
  strength: number;
  touches: number;
}

interface AtlasChartAnalysisPanelProps {
  symbol?: string;
  isVisible?: boolean;
  height?: number;
}

// Styled Components
const PanelContainer = styled(Paper)(() => ({
  background: 'var(--atlas-gradient-card)',
  border: '1px solid var(--atlas-border-primary)',
  borderRadius: '12px',
  overflow: 'hidden',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
}));

const PanelHeader = styled(Box)(({ theme }) => ({
  background: 'var(--atlas-bg-secondary)',
  borderBottom: '1px solid var(--atlas-border-primary)',
  padding: theme.spacing(1, 2),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
}));

const TabPanel = styled(Box)(({ theme }) => ({
  flex: 1,
  overflow: 'auto',
  padding: theme.spacing(1),
}));

const AtlasChartAnalysisPanel: React.FC<AtlasChartAnalysisPanelProps> = ({
  symbol = 'AAPL',
  isVisible = true,
  height = 600,
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [selectedSymbol, setSelectedSymbol] = useState(symbol);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Mock data (replace with real API calls)
  const [chartData] = useState<ChartData[]>([
    { timestamp: Date.now() - 86400000 * 30, open: 150, high: 155, low: 148, close: 152, volume: 1000000 },
    { timestamp: Date.now() - 86400000 * 29, open: 152, high: 158, low: 151, close: 156, volume: 1200000 },
    { timestamp: Date.now() - 86400000 * 28, open: 156, high: 160, low: 154, close: 159, volume: 1100000 },
    { timestamp: Date.now() - 86400000 * 27, open: 159, high: 162, low: 157, close: 161, volume: 1300000 },
    { timestamp: Date.now() - 86400000 * 26, open: 161, high: 165, low: 159, close: 163, volume: 1400000 },
    { timestamp: Date.now() - 86400000 * 25, open: 163, high: 167, low: 161, close: 165, volume: 1250000 },
    { timestamp: Date.now() - 86400000 * 24, open: 165, high: 169, low: 163, close: 167, volume: 1350000 },
    { timestamp: Date.now() - 86400000 * 23, open: 167, high: 171, low: 165, close: 169, volume: 1450000 },
    { timestamp: Date.now() - 86400000 * 22, open: 169, high: 173, low: 167, close: 171, volume: 1550000 },
    { timestamp: Date.now() - 86400000 * 21, open: 171, high: 175, low: 169, close: 173, volume: 1650000 },
  ]);

  const [technicalIndicators] = useState<TechnicalIndicator[]>([
    { name: 'RSI (14)', value: 65.4, signal: 'hold', strength: 65, description: 'Relative Strength Index indicates neutral momentum' },
    { name: 'MACD', value: 2.3, signal: 'buy', strength: 78, description: 'MACD line above signal line, bullish momentum' },
    { name: 'SMA (20)', value: 168.5, signal: 'buy', strength: 72, description: 'Price above 20-day Simple Moving Average' },
    { name: 'SMA (50)', value: 165.2, signal: 'buy', strength: 68, description: 'Price above 50-day Simple Moving Average' },
    { name: 'Bollinger Bands', value: 0.85, signal: 'hold', strength: 55, description: 'Price in middle of Bollinger Bands' },
    { name: 'Stochastic', value: 72.1, signal: 'sell', strength: 62, description: 'Stochastic oscillator in overbought territory' },
  ]);

  // Convert to chart display format (add confidence field)
  const chartIndicators = technicalIndicators.map(indicator => ({
    ...indicator,
    confidence: indicator.strength / 100
  }));

  const [patterns] = useState<PatternAnalysis[]>([
    { pattern: 'Ascending Triangle', confidence: 78, timeframe: '1D', description: 'Bullish continuation pattern forming', signal: 'bullish' },
    { pattern: 'Golden Cross', confidence: 85, timeframe: '4H', description: '50-day MA crossed above 200-day MA', signal: 'bullish' },
    { pattern: 'Higher Highs', confidence: 72, timeframe: '1H', description: 'Series of higher highs and higher lows', signal: 'bullish' },
  ]);

  const [supportResistance] = useState<SupportResistance[]>([
    { type: 'resistance', level: 175.50, strength: 85, touches: 3 },
    { type: 'support', level: 168.20, strength: 78, touches: 4 },
    { type: 'resistance', level: 172.80, strength: 65, touches: 2 },
    { type: 'support', level: 165.00, strength: 92, touches: 5 },
  ]);

  // Calculate overall signal
  const buySignals = technicalIndicators.filter(i => i.signal === 'buy').length;
  const sellSignals = technicalIndicators.filter(i => i.signal === 'sell').length;
  const holdSignals = technicalIndicators.filter(i => i.signal === 'hold').length;
  
  const overallSignal = buySignals > sellSignals ? 'buy' : sellSignals > buySignals ? 'sell' : 'hold';
  const confidence = Math.round(((Math.max(buySignals, sellSignals, holdSignals) / technicalIndicators.length) * 100));

  // Handle symbol change
  const handleSymbolChange = (newSymbol: string) => {
    setSelectedSymbol(newSymbol);
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => setIsLoading(false), 1000);
  };

  // Handle refresh
  const handleRefresh = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => setIsLoading(false), 1000);
  };

  // Get current price
  const currentPrice = chartData.length > 0 ? chartData[chartData.length - 1].close : 0;

  if (!isVisible) return null;

  return (
    <PanelContainer elevation={2} sx={{ height: isFullscreen ? '100vh' : height }}>
      {/* Header */}
      <PanelHeader>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <ShowChartIcon color="primary" />
          <Typography variant="h6" color="primary">
            Chart & Analysis
          </Typography>
          
          {/* Symbol Selector */}
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Symbol</InputLabel>
            <Select
              value={selectedSymbol}
              onChange={(e) => handleSymbolChange(e.target.value)}
              label="Symbol"
              sx={{ color: 'white' }}
            >
              {['AAPL', 'TSLA', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'NFLX'].map(sym => (
                <MenuItem key={sym} value={sym}>{sym}</MenuItem>
              ))}
            </Select>
          </FormControl>

          <Chip
            label={`$${currentPrice.toFixed(2)}`}
            color="primary"
            size="small"
          />
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title="Refresh Data">
            <IconButton size="small" onClick={handleRefresh} disabled={isLoading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
            <IconButton size="small" onClick={() => setIsFullscreen(!isFullscreen)}>
              {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
            </IconButton>
          </Tooltip>
        </Box>
      </PanelHeader>

      {/* Tabs */}
      <Box sx={{ borderBottom: '1px solid var(--atlas-border-primary)' }}>
        <Tabs
          value={activeTab}
          onChange={(_, newValue) => setActiveTab(newValue)}
          sx={{
            '& .MuiTab-root': {
              color: 'var(--atlas-text-secondary)',
              '&.Mui-selected': {
                color: 'var(--atlas-accent-primary)',
              },
            },
          }}
        >
          <Tab
            icon={<TimelineIcon />}
            label="Chart"
            iconPosition="start"
            sx={{ minHeight: 48 }}
          />
          <Tab
            icon={<AnalyticsIcon />}
            label="Technical Analysis"
            iconPosition="start"
            sx={{ minHeight: 48 }}
          />
          <Tab
            icon={<AssessmentIcon />}
            label="Combined View"
            iconPosition="start"
            sx={{ minHeight: 48 }}
          />
        </Tabs>
      </Box>

      {/* Tab Content */}
      <TabPanel>
        {activeTab === 0 && (
          <AtlasChartDisplay
            symbol={selectedSymbol}
            data={chartData}
            indicators={chartIndicators}
            height={isFullscreen ? window.innerHeight - 200 : height - 200}
            isFullscreen={isFullscreen}
            onFullscreenToggle={() => setIsFullscreen(!isFullscreen)}
          />
        )}

        {activeTab === 1 && (
          <AtlasTechnicalAnalysis
            symbol={selectedSymbol}
            currentPrice={currentPrice}
            indicators={technicalIndicators}
            patterns={patterns}
            supportResistance={supportResistance}
            overallSignal={overallSignal as any}
            confidence={confidence}
          />
        )}

        {activeTab === 2 && (
          <Box sx={{ display: 'flex', gap: 2, height: '100%' }}>
            <Box sx={{ flex: 2 }}>
              <AtlasChartDisplay
                symbol={selectedSymbol}
                data={chartData}
                indicators={chartIndicators}
                height={isFullscreen ? window.innerHeight - 300 : height - 300}
                isFullscreen={false}
              />
            </Box>
            <Box sx={{ flex: 1, minWidth: 300 }}>
              <AtlasTechnicalAnalysis
                symbol={selectedSymbol}
                currentPrice={currentPrice}
                indicators={technicalIndicators}
                patterns={patterns}
                supportResistance={supportResistance}
                overallSignal={overallSignal as any}
                confidence={confidence}
              />
            </Box>
          </Box>
        )}
      </TabPanel>
    </PanelContainer>
  );
};

export default AtlasChartAnalysisPanel;
