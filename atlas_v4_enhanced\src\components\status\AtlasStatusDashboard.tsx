import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  IconButton,
  <PERSON><PERSON><PERSON>,
  <PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  VisibilityOff as VisibilityOffIcon,
  Psychology as PsychologyIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Import our new components
import AtlasProgressIndicator from '../progress/AtlasProgressIndicator';
import AtlasTerminalOutput from '../terminal/AtlasTerminalOutput';
import AtlasConversationMonitor from '../monitoring/AtlasConversationMonitor';
import { useAtlasSystem } from '../../hooks/useAtlasSystem';
import { useAtlasWebSocket } from '../../hooks/useAtlasWebSocket';

// Types
interface DashboardSettings {
  showProgress: boolean;
  showTerminal: boolean;
  showMonitoring: boolean;
  compactMode: boolean;
  autoHide: boolean;
  terminalHeight: number;
}

interface AtlasStatusDashboardProps {
  isVisible?: boolean;
  onToggleVisibility?: () => void;
  sessionId?: string;
  userId?: string;
}

// Styled Components
const DashboardContainer = styled(Box)(() => ({
  position: 'fixed',
  top: 0,
  right: 0,
  width: '400px',
  height: '100vh',
  background: 'var(--atlas-bg-primary)',
  borderLeft: '1px solid var(--atlas-border-primary)',
  zIndex: 1300,
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.3s ease-in-out',
}));

const DashboardHeader = styled(AppBar)(() => ({
  background: 'var(--atlas-gradient-primary)',
  boxShadow: 'none',
  borderBottom: '1px solid var(--atlas-border-primary)',
}));

const DashboardContent = styled(Box)(({ theme }) => ({
  flex: 1,
  overflowY: 'auto',
  padding: theme.spacing(1),
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'rgba(255, 255, 255, 0.1)',
  },
  '&::-webkit-scrollbar-thumb': {
    background: 'var(--atlas-accent-primary)',
    borderRadius: '3px',
  },
}));

const AtlasStatusDashboard: React.FC<AtlasStatusDashboardProps> = ({
  isVisible = false,
  onToggleVisibility,
  sessionId,
  userId,
}) => {
  const [settings, setSettings] = useState<DashboardSettings>({
    showProgress: true,
    showTerminal: true,
    showMonitoring: true,
    compactMode: false,
    autoHide: false,
    terminalHeight: 200,
  });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);

  // System hooks
  const { grokStatus, isSystemHealthy } = useAtlasSystem();
  const { isConnected } = useAtlasWebSocket();

  // Mock data for demonstration (replace with real data)
  const [progressStages] = useState([
    {
      id: 'intent',
      name: 'Intent Analysis',
      description: 'Analyzing user request with Grok AI',
      status: 'completed' as const,
      progress: 100,
      duration: 245,
    },
    {
      id: 'symbol',
      name: 'Symbol Extraction',
      description: 'Extracting trading symbols and context',
      status: 'completed' as const,
      progress: 100,
      duration: 156,
    },
    {
      id: 'validation',
      name: 'Data Validation',
      description: 'Validating market data and parameters',
      status: 'active' as const,
      progress: 75,
    },
    {
      id: 'analysis',
      name: 'Market Analysis',
      description: 'Performing technical and fundamental analysis',
      status: 'pending' as const,
      progress: 0,
    },
  ]);

  const [terminalMessages] = useState([
    {
      id: '1',
      timestamp: new Date(),
      level: 'info' as const,
      source: 'GROK_AI',
      message: 'Enhanced analysis request received for AAPL',
    },
    {
      id: '2',
      timestamp: new Date(),
      level: 'success' as const,
      source: 'SCANNER',
      message: 'Lee Method signal detected: TSLA bullish pattern',
    },
    {
      id: '3',
      timestamp: new Date(),
      level: 'warning' as const,
      source: 'RISK_MGR',
      message: 'Portfolio exposure approaching 80% threshold',
    },
  ]);

  const [conversationMetrics] = useState({
    totalMessages: 24,
    userMessages: 12,
    assistantMessages: 12,
    averageResponseTime: 1250,
    grokEnhancedResponses: 8,
    successfulTrades: 3,
    riskAlerts: 1,
    sessionDuration: 1847,
  });

  const [conversationEvents] = useState([
    {
      id: '1',
      timestamp: new Date(),
      type: 'message' as const,
      severity: 'low' as const,
      description: 'User requested AAPL analysis',
      userId: userId,
    },
    {
      id: '2',
      timestamp: new Date(),
      type: 'analysis' as const,
      severity: 'medium' as const,
      description: 'Grok AI enhanced analysis completed',
      responseTime: 1250,
    },
  ]);

  // Toggle fullscreen
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Update setting
  const updateSetting = (key: keyof DashboardSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  // Calculate overall progress
  const overallProgress = progressStages.reduce((acc, stage) => {
    return acc + (stage.progress || 0);
  }, 0) / progressStages.length;

  if (!isVisible) return null;

  return (
    <>
      <DashboardContainer
        sx={{
          width: isFullscreen ? '100vw' : '400px',
          transform: isVisible ? 'translateX(0)' : 'translateX(100%)',
        }}
      >
        {/* Header */}
        <DashboardHeader position="static">
          <Toolbar variant="dense">
            <DashboardIcon sx={{ marginRight: 1 }} />
            <Typography variant="h6" sx={{ flexGrow: 1 }}>
              A.T.L.A.S. Status
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {/* Status Indicators */}
              <Badge color={isSystemHealthy ? "success" : "error"} variant="dot">
                <Chip
                  label="System"
                  size="small"
                  color={isSystemHealthy ? "success" : "error"}
                />
              </Badge>
              
              <Badge color={grokStatus?.available ? "success" : "warning"} variant="dot">
                <Chip
                  icon={<PsychologyIcon />}
                  label="Grok"
                  size="small"
                  color={grokStatus?.available ? "success" : "warning"}
                />
              </Badge>
              
              <Badge color={isConnected ? "success" : "error"} variant="dot">
                <Chip
                  label="WS"
                  size="small"
                  color={isConnected ? "success" : "error"}
                />
              </Badge>

              {/* Controls */}
              <Tooltip title="Settings">
                <IconButton
                  size="small"
                  onClick={() => setSettingsOpen(true)}
                  sx={{ color: 'white' }}
                >
                  <SettingsIcon />
                </IconButton>
              </Tooltip>

              <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
                <IconButton
                  size="small"
                  onClick={toggleFullscreen}
                  sx={{ color: 'white' }}
                >
                  {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
                </IconButton>
              </Tooltip>

              <Tooltip title="Hide Dashboard">
                <IconButton
                  size="small"
                  onClick={onToggleVisibility}
                  sx={{ color: 'white' }}
                >
                  <VisibilityOffIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Toolbar>
        </DashboardHeader>

        {/* Content */}
        <DashboardContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            {/* Progress Indicator */}
            {settings.showProgress && (
              <AtlasProgressIndicator
                stages={progressStages}
                overallProgress={overallProgress}
                title="Current Processing"
                compact={settings.compactMode}
              />
            )}

            {/* Terminal Output */}
            {settings.showTerminal && (
              <AtlasTerminalOutput
                messages={terminalMessages}
                height={settings.terminalHeight}
                title="System Logs"
              />
            )}

            {/* Conversation Monitor */}
            {settings.showMonitoring && (
              <AtlasConversationMonitor
                metrics={conversationMetrics}
                events={conversationEvents}
                sessionId={sessionId}
                userId={userId}
              />
            )}
          </Box>
        </DashboardContent>
      </DashboardContainer>

      {/* Settings Drawer */}
      <Drawer
        anchor="right"
        open={settingsOpen}
        onClose={() => setSettingsOpen(false)}
        sx={{ zIndex: 1400 }}
      >
        <Box sx={{ width: 300, padding: 2 }}>
          <Typography variant="h6" gutterBottom>
            Dashboard Settings
          </Typography>
          
          <FormControlLabel
            control={
              <Switch
                checked={settings.showProgress}
                onChange={(e) => updateSetting('showProgress', e.target.checked)}
              />
            }
            label="Show Progress Indicators"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={settings.showTerminal}
                onChange={(e) => updateSetting('showTerminal', e.target.checked)}
              />
            }
            label="Show Terminal Output"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={settings.showMonitoring}
                onChange={(e) => updateSetting('showMonitoring', e.target.checked)}
              />
            }
            label="Show Conversation Monitor"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={settings.compactMode}
                onChange={(e) => updateSetting('compactMode', e.target.checked)}
              />
            }
            label="Compact Mode"
          />
        </Box>
      </Drawer>
    </>
  );
};

export default AtlasStatusDashboard;
