import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Badge,

  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Person as PersonIcon,
  SmartToy as SmartToyIcon,
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Types
interface ConversationMetrics {
  totalMessages: number;
  userMessages: number;
  assistantMessages: number;
  averageResponseTime: number;
  grokEnhancedResponses: number;
  successfulTrades: number;
  riskAlerts: number;
  sessionDuration: number;
}

interface ConversationEvent {
  id: string;
  timestamp: Date;
  type: 'message' | 'trade' | 'alert' | 'analysis' | 'error' | 'system';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  details?: any;
  userId?: string;
  responseTime?: number;
}

interface AtlasConversationMonitorProps {
  metrics: ConversationMetrics;
  events: ConversationEvent[];
  isActive?: boolean;
  sessionId?: string;
  userId?: string;
}

// Styled Components
const MonitorContainer = styled(Paper)(({ theme }) => ({
  background: 'var(--atlas-gradient-card)',
  border: '1px solid var(--atlas-border-primary)',
  borderRadius: '12px',
  padding: theme.spacing(2),
  height: '100%',
}));

const MetricCard = styled(Box)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.05)',
  border: '1px solid var(--atlas-border-secondary)',
  borderRadius: '8px',
  padding: theme.spacing(1.5),
  textAlign: 'center',
  minHeight: '80px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
}));

const EventItem = styled(ListItem)<{ severity: string }>(({ severity }) => ({
  borderRadius: '6px',
  marginBottom: '4px',
  backgroundColor: 
    severity === 'critical' ? 'rgba(255, 82, 82, 0.1)' :
    severity === 'high' ? 'rgba(255, 170, 0, 0.1)' :
    severity === 'medium' ? 'rgba(0, 153, 255, 0.1)' :
    'rgba(255, 255, 255, 0.02)',
  border: 
    severity === 'critical' ? '1px solid rgba(255, 82, 82, 0.3)' :
    severity === 'high' ? '1px solid rgba(255, 170, 0, 0.3)' :
    severity === 'medium' ? '1px solid rgba(0, 153, 255, 0.3)' :
    '1px solid transparent',
}));

const AtlasConversationMonitor: React.FC<AtlasConversationMonitorProps> = ({
  metrics,
  events,
  isActive = true,
  sessionId,
  userId,
}) => {
  const [recentEvents, setRecentEvents] = useState<ConversationEvent[]>([]);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['metrics']));

  // Filter recent events (last 50)
  useEffect(() => {
    const sorted = [...events].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    setRecentEvents(sorted.slice(0, 50));
  }, [events]);

  // Toggle section expansion
  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  // Get event icon
  const getEventIcon = (event: ConversationEvent) => {
    switch (event.type) {
      case 'message':
        return event.userId ? <PersonIcon /> : <SmartToyIcon />;
      case 'trade':
        return <TrendingUpIcon />;
      case 'alert':
        return <WarningIcon />;
      case 'analysis':
        return <PsychologyIcon />;
      case 'error':
        return <WarningIcon color="error" />;
      case 'system':
        return <CheckCircleIcon />;
      default:
        return <ScheduleIcon />;
    }
  };

  // Get severity color
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return '#ff5252';
      case 'high': return '#ffaa00';
      case 'medium': return '#0099ff';
      default: return '#888888';
    }
  };

  // Format duration
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  // Calculate response time performance
  const getResponseTimeStatus = (avgTime: number) => {
    if (avgTime < 1000) return { color: '#00ff88', label: 'Excellent' };
    if (avgTime < 3000) return { color: '#0099ff', label: 'Good' };
    if (avgTime < 5000) return { color: '#ffaa00', label: 'Fair' };
    return { color: '#ff5252', label: 'Slow' };
  };

  const responseTimeStatus = getResponseTimeStatus(metrics.averageResponseTime);

  return (
    <MonitorContainer elevation={2}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <VisibilityIcon color="primary" />
          <Typography variant="h6" color="primary">
            Conversation Monitor
          </Typography>
          <Badge
            color={isActive ? "success" : "error"}
            variant="dot"
            sx={{ marginLeft: 1 }}
          />
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Chip
            label={`Session: ${sessionId?.slice(-8) || 'Unknown'}`}
            size="small"
            variant="outlined"
          />
          {userId && (
            <Chip
              label={`User: ${userId.slice(0, 8)}`}
              size="small"
              color="primary"
            />
          )}
        </Box>
      </Box>

      {/* Metrics Section */}
      <Accordion 
        expanded={expandedSections.has('metrics')} 
        onChange={() => toggleSection('metrics')}
        sx={{ marginBottom: 2, backgroundColor: 'transparent' }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1" color="primary">
            Session Metrics
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', gap: 2 }}>
            <MetricCard>
              <Typography variant="h4" color="primary">
                {metrics.totalMessages}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Total Messages
              </Typography>
            </MetricCard>

            <MetricCard>
              <Typography variant="h4" sx={{ color: responseTimeStatus.color }}>
                {Math.round(metrics.averageResponseTime)}ms
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Avg Response ({responseTimeStatus.label})
              </Typography>
            </MetricCard>

            <MetricCard>
              <Typography variant="h4" color="success.main">
                {metrics.grokEnhancedResponses}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Grok Enhanced
              </Typography>
            </MetricCard>

            <MetricCard>
              <Typography variant="h4" color="warning.main">
                {metrics.successfulTrades}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Successful Trades
              </Typography>
            </MetricCard>

            <MetricCard>
              <Typography variant="h4" color="error.main">
                {metrics.riskAlerts}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Risk Alerts
              </Typography>
            </MetricCard>

            <MetricCard>
              <Typography variant="h4" color="info.main">
                {formatDuration(metrics.sessionDuration)}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Session Duration
              </Typography>
            </MetricCard>
          </Box>

          {/* Performance Indicators */}
          <Box sx={{ marginTop: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Performance Indicators
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, marginBottom: 1 }}>
              <SpeedIcon fontSize="small" />
              <Typography variant="body2" sx={{ minWidth: 120 }}>
                Response Time:
              </Typography>
              <LinearProgress
                variant="determinate"
                value={Math.min(100, (5000 - metrics.averageResponseTime) / 50)}
                sx={{ flex: 1, marginRight: 1 }}
              />
              <Typography variant="caption" sx={{ color: responseTimeStatus.color }}>
                {responseTimeStatus.label}
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, marginBottom: 1 }}>
              <PsychologyIcon fontSize="small" />
              <Typography variant="body2" sx={{ minWidth: 120 }}>
                AI Enhancement:
              </Typography>
              <LinearProgress
                variant="determinate"
                value={(metrics.grokEnhancedResponses / Math.max(1, metrics.assistantMessages)) * 100}
                sx={{ flex: 1, marginRight: 1 }}
              />
              <Typography variant="caption">
                {Math.round((metrics.grokEnhancedResponses / Math.max(1, metrics.assistantMessages)) * 100)}%
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <SecurityIcon fontSize="small" />
              <Typography variant="body2" sx={{ minWidth: 120 }}>
                Risk Level:
              </Typography>
              <LinearProgress
                variant="determinate"
                value={Math.min(100, (metrics.riskAlerts / Math.max(1, metrics.totalMessages)) * 1000)}
                color={metrics.riskAlerts > 5 ? "error" : metrics.riskAlerts > 2 ? "warning" : "success"}
                sx={{ flex: 1, marginRight: 1 }}
              />
              <Typography variant="caption">
                {metrics.riskAlerts > 5 ? 'High' : metrics.riskAlerts > 2 ? 'Medium' : 'Low'}
              </Typography>
            </Box>
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* Recent Events */}
      <Accordion 
        expanded={expandedSections.has('events')} 
        onChange={() => toggleSection('events')}
        sx={{ backgroundColor: 'transparent' }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1" color="primary">
            Recent Events ({recentEvents.length})
          </Typography>
        </AccordionSummary>
        <AccordionDetails sx={{ maxHeight: 300, overflowY: 'auto' }}>
          <List dense>
            {recentEvents.map((event) => (
              <EventItem key={event.id} severity={event.severity}>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <Avatar
                    sx={{
                      width: 24,
                      height: 24,
                      backgroundColor: getSeverityColor(event.severity),
                      fontSize: '12px',
                    }}
                  >
                    {getEventIcon(event)}
                  </Avatar>
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2" fontWeight={500}>
                        {event.description}
                      </Typography>
                      <Chip
                        label={event.type.toUpperCase()}
                        size="small"
                        sx={{ fontSize: '0.6rem', height: 18 }}
                      />
                      {event.responseTime && (
                        <Chip
                          label={`${event.responseTime}ms`}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.6rem', height: 18 }}
                        />
                      )}
                    </Box>
                  }
                  secondary={
                    <Typography variant="caption" color="textSecondary">
                      {event.timestamp.toLocaleTimeString()}
                    </Typography>
                  }
                />
              </EventItem>
            ))}
            
            {recentEvents.length === 0 && (
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                padding: 4,
                color: 'text.secondary'
              }}>
                <Typography variant="body2">
                  No recent events
                </Typography>
              </Box>
            )}
          </List>
        </AccordionDetails>
      </Accordion>
    </MonitorContainer>
  );
};

export default AtlasConversationMonitor;
