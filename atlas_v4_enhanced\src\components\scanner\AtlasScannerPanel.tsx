import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Chip,
  List,
  ListItem,
  ListItemText,
  Tooltip,
  LinearProgress,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  List as ListIcon,
  Speed as SpeedIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

import { LeeMethodSignal, ScannerStats } from '../../types/atlas';
import { atlasApi } from '../../services/atlasApi';

const ScannerContainer = styled(Box)(() => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  background: 'var(--atlas-bg-secondary)',
}));

const ScannerHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  borderBottom: '1px solid var(--atlas-border-primary)',
  background: 'var(--atlas-bg-tertiary)',
}));

const StatsContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  padding: theme.spacing(2, 3),
  background: 'var(--atlas-bg-primary)',
  borderBottom: '1px solid var(--atlas-border-primary)',
}));

const StatItem = styled(Box)(() => ({
  textAlign: 'center',
}));

const StatValue = styled(Typography)(() => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: 'var(--atlas-primary)',
  fontFamily: 'var(--atlas-font-mono)',
}));

const StatLabel = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  color: 'var(--atlas-text-secondary)',
  marginTop: theme.spacing(0.5),
}));

const ControlsContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  borderBottom: '1px solid var(--atlas-border-primary)',
}));

const SignalsList = styled(List)(() => ({
  flex: 1,
  overflowY: 'auto',
  padding: 0,
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'var(--atlas-bg-secondary)',
  },
  '&::-webkit-scrollbar-thumb': {
    background: 'var(--atlas-primary)',
    borderRadius: '3px',
  },
}));

const SignalItem = styled(ListItem)(() => ({
  borderBottom: '1px solid var(--atlas-border-primary)',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: 'var(--atlas-bg-hover)',
    borderLeft: '3px solid var(--atlas-primary)',
  },
}));

interface AtlasScannerPanelProps {
  scannerData: LeeMethodSignal[];
  isConnected: boolean;
}

const AtlasScannerPanel: React.FC<AtlasScannerPanelProps> = ({
  scannerData,
  isConnected,
}) => {
  const [stats, setStats] = useState<ScannerStats>({
    active_signals: 0,
    pattern_accuracy: 87,
    scans_today: 1247,
    market_status: 'OPEN',
    last_update: new Date().toISOString(),
  });
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Update stats when scanner data changes
  useEffect(() => {
    setStats(prev => ({
      ...prev,
      active_signals: scannerData.length,
      last_update: new Date().toISOString(),
    }));
  }, [scannerData]);

  // Refresh scanner data
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      const response = await atlasApi.scanMarket();
      if (response.success) {
        console.log('📊 Scanner refreshed:', response.data);
      }
    } catch (error) {
      console.error('❌ Scanner refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Get strength color
  const getStrengthColor = (strength: string) => {
    switch (strength) {
      case 'VERY_STRONG':
        return '#00ff88';
      case 'STRONG':
        return '#33ffaa';
      case 'MEDIUM':
        return '#ffaa00';
      case 'WEAK':
        return '#ff6666';
      default:
        return '#8892b0';
    }
  };

  // Get strength label
  const getStrengthLabel = (confidence: number) => {
    if (confidence >= 0.9) return 'VERY_STRONG';
    if (confidence >= 0.75) return 'STRONG';
    if (confidence >= 0.6) return 'MEDIUM';
    return 'WEAK';
  };

  return (
    <ScannerContainer>
      {/* Header */}
      <ScannerHeader>
        <Typography
          variant="h6"
          sx={{
            color: 'var(--atlas-primary)',
            fontWeight: 600,
            marginBottom: 1,
          }}
        >
          Lee Method Scanner
        </Typography>
        <Typography
          variant="body2"
          sx={{
            color: 'var(--atlas-text-secondary)',
          }}
        >
          Live S&P 500 Pattern Detection
        </Typography>
      </ScannerHeader>

      {/* Statistics */}
      <StatsContainer>
        <StatItem>
          <StatValue>{stats.active_signals}</StatValue>
          <StatLabel>Active Signals</StatLabel>
        </StatItem>
        <StatItem>
          <StatValue>{stats.pattern_accuracy}%</StatValue>
          <StatLabel>Pattern Accuracy</StatLabel>
        </StatItem>
        <StatItem>
          <StatValue>{stats.scans_today.toLocaleString()}</StatValue>
          <StatLabel>Scans Today</StatLabel>
        </StatItem>
      </StatsContainer>

      {/* Controls */}
      <ControlsContainer>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Refresh Scanner">
            <IconButton
              onClick={handleRefresh}
              disabled={isRefreshing}
              sx={{
                backgroundColor: 'var(--atlas-primary)',
                color: 'var(--atlas-bg-primary)',
                '&:hover': {
                  backgroundColor: 'var(--atlas-primary-dark)',
                  boxShadow: 'var(--atlas-glow-primary)',
                },
                '&:disabled': {
                  backgroundColor: 'var(--atlas-bg-tertiary)',
                  color: 'var(--atlas-text-muted)',
                },
              }}
            >
              <RefreshIcon className={isRefreshing ? 'atlas-pulse' : ''} />
            </IconButton>
          </Tooltip>

          <Tooltip title="Scanner Settings">
            <IconButton
              sx={{
                backgroundColor: 'var(--atlas-bg-tertiary)',
                color: 'var(--atlas-text-secondary)',
                '&:hover': {
                  backgroundColor: 'var(--atlas-bg-hover)',
                  color: 'var(--atlas-primary)',
                },
              }}
            >
              <SettingsIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="View Criteria">
            <IconButton
              sx={{
                backgroundColor: 'var(--atlas-bg-tertiary)',
                color: 'var(--atlas-text-secondary)',
                '&:hover': {
                  backgroundColor: 'var(--atlas-bg-hover)',
                  color: 'var(--atlas-primary)',
                },
              }}
            >
              <ListIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Connection Status */}
        <Box sx={{ marginTop: 2 }}>
          <Chip
            icon={isConnected ? <SpeedIcon /> : <VisibilityIcon />}
            label={isConnected ? 'Connected' : 'Disconnected'}
            size="small"
            sx={{
              backgroundColor: isConnected 
                ? 'rgba(0, 255, 136, 0.1)' 
                : 'rgba(255, 51, 102, 0.1)',
              color: isConnected ? '#00ff88' : '#ff3366',
              border: `1px solid ${isConnected 
                ? 'rgba(0, 255, 136, 0.3)' 
                : 'rgba(255, 51, 102, 0.3)'}`,
            }}
          />
        </Box>
      </ControlsContainer>

      {/* Signals List */}
      <SignalsList>
        {scannerData.length === 0 ? (
          <Box sx={{ padding: 3, textAlign: 'center' }}>
            <Typography variant="body2" color="textSecondary">
              {isConnected ? 'Scanning for patterns...' : 'No signals available'}
            </Typography>
            {isConnected && (
              <LinearProgress
                sx={{
                  marginTop: 2,
                  backgroundColor: 'rgba(0, 255, 136, 0.1)',
                  '& .MuiLinearProgress-bar': {
                    background: 'var(--atlas-gradient-primary)',
                  },
                }}
              />
            )}
          </Box>
        ) : (
          scannerData.map((signal, index) => (
            <SignalItem key={`${signal.symbol}-${index}`}>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h6" sx={{ color: 'var(--atlas-text-primary)' }}>
                      {signal.symbol}
                    </Typography>
                    <Typography variant="h6" sx={{ color: 'var(--atlas-primary)' }}>
                      ${signal.price?.toFixed(2) || '--'}
                    </Typography>
                  </Box>
                }
                secondary={
                  <Box sx={{ marginTop: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" sx={{ color: 'var(--atlas-text-secondary)' }}>
                        Confidence: {(signal.confidence * 100).toFixed(1)}%
                      </Typography>
                      <Chip
                        label={getStrengthLabel(signal.confidence)}
                        size="small"
                        sx={{
                          backgroundColor: `${getStrengthColor(getStrengthLabel(signal.confidence))}20`,
                          color: getStrengthColor(getStrengthLabel(signal.confidence)),
                          border: `1px solid ${getStrengthColor(getStrengthLabel(signal.confidence))}50`,
                          fontSize: '0.7rem',
                        }}
                      />
                    </Box>
                    <Typography variant="caption" sx={{ color: 'var(--atlas-text-muted)' }}>
                      {signal.pattern_type} • {new Date(signal.timestamp).toLocaleTimeString()}
                    </Typography>
                  </Box>
                }
              />
            </SignalItem>
          ))
        )}
      </SignalsList>
    </ScannerContainer>
  );
};

export default AtlasScannerPanel;
