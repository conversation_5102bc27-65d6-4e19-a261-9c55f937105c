// A.T.L.A.S. Type Definitions - Complete preservation of all system types
export interface AtlasMessage {
  id: string;
  type: 'user' | 'assistant' | 'system' | 'trading_signal' | 'scanner_alert' | 'news_insight' | 'progress' | 'chart_analysis';
  content: string;
  timestamp: Date;
  metadata?: {
    symbol?: string;
    confidence?: number;
    signal_type?: string;
    price?: number;
    pattern_type?: string;
    progress_stage?: string;
    progress_percentage?: number;
    chart_data?: any;
    news_sentiment?: number;
    grok_enhanced?: boolean;
  };
  attachments?: AtlasAttachment[];
}

export interface AtlasAttachment {
  id: string;
  type: 'chart' | 'document' | 'trading_signal' | 'technical_analysis' | 'news_article';
  url?: string;
  data?: any;
  title?: string;
  description?: string;
}

export interface LeeMethodSignal {
  symbol: string;
  price: number;
  confidence: number;
  pattern_type: 'Lee Method' | 'TTM Squeeze';
  timestamp: string;
  criteria_met: {
    trend_confirmation: boolean;
    volume_validation: boolean;
    technical_pattern: boolean;
  };
  strength: 'WEAK' | 'MEDIUM' | 'STRONG' | 'VERY_STRONG';
}

export interface ScannerStats {
  active_signals: number;
  pattern_accuracy: number;
  scans_today: number;
  market_status: 'OPEN' | 'CLOSED' | 'PRE_MARKET' | 'AFTER_HOURS';
  last_update: string;
}

export interface TradingSignal {
  symbol: string;
  action: 'BUY' | 'SELL' | 'HOLD';
  confidence: number;
  entry_price: number;
  target_price: number;
  stop_loss: number;
  position_size: number;
  risk_reward_ratio: number;
  analysis: string;
  timestamp: string;
}

export interface NewsInsight {
  id: string;
  title: string;
  summary: string;
  sentiment: number; // -1 to 1
  relevance: number; // 0 to 1
  symbols_mentioned: string[];
  source: string;
  timestamp: string;
  impact_score: number;
}

export interface ProgressUpdate {
  stage: string;
  percentage: number;
  message: string;
  timestamp: string;
  status: 'processing' | 'completed' | 'failed';
}

export interface SystemStatus {
  backend_health: 'healthy' | 'degraded' | 'unhealthy';
  api_endpoints: number;
  websocket_connections: number;
  database_status: 'connected' | 'disconnected';
  grok_status: 'available' | 'fallback' | 'unavailable';
  scanner_status: 'active' | 'inactive' | 'error';
  last_health_check: string;
}

export interface ChatSession {
  id: string;
  messages: AtlasMessage[];
  created_at: Date;
  updated_at: Date;
  user_preferences?: {
    theme: 'dark' | 'light' | 'cyberpunk';
    notifications: boolean;
    auto_trading: boolean;
    risk_level: 'conservative' | 'moderate' | 'aggressive';
  };
}

export interface WebSocketMessage {
  type: 'scanner_update' | 'trading_signal' | 'news_alert' | 'progress_update' | 'system_status' | 'ping' | 'pong';
  data: any;
  timestamp: string;
}

// Stream Chat integration types
export interface StreamChatConfig {
  apiKey: string;
  userId: string;
  userToken: string;
  userName: string;
}

// A.T.L.A.S. API Response types (preserve all existing formats)
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
  processing_time?: number;
}

export interface ChatApiResponse extends ApiResponse {
  data: {
    response: string;
    confidence: number;
    analysis_type: string;
    grok_enhanced: boolean;
    fallback_used: boolean;
    processing_stages: string[];
  };
}

export interface ScannerApiResponse extends ApiResponse {
  data: {
    signals: LeeMethodSignal[];
    stats: ScannerStats;
    scan_time: number;
  };
}

export interface MarketDataResponse extends ApiResponse {
  data: {
    symbol: string;
    price: number;
    change: number;
    change_percent: number;
    volume: number;
    market_cap?: number;
    pe_ratio?: number;
    technical_indicators?: {
      rsi: number;
      macd: number;
      bollinger_bands: {
        upper: number;
        middle: number;
        lower: number;
      };
    };
  };
}
