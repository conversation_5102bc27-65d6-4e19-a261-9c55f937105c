"""
A.T.L.A.S. Web Search Service
Centralized web search functionality for all system components
"""

import asyncio
import aiohttp
import hashlib
import json
import logging
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from urllib.parse import quote_plus

from config import settings

logger = logging.getLogger(__name__)

class SearchContext(Enum):
    """Search context types for different system components"""
    GENERAL = "general"
    MARKET_ANALYSIS = "market_analysis"
    NEWS_SENTIMENT = "news_sentiment"
    RISK_MANAGEMENT = "risk_management"
    TRADING_SIGNALS = "trading_signals"
    EDUCATION = "education"
    LEE_METHOD = "lee_method"
    PORTFOLIO_ANALYSIS = "portfolio_analysis"
    AI_CONVERSATION = "ai_conversation"

@dataclass
class SearchResult:
    """Web search result data structure"""
    title: str
    snippet: str
    url: str
    source: str
    published_date: Optional[datetime]
    relevance_score: float
    search_context: SearchContext
    metadata: Dict[str, Any]

@dataclass
class SearchQuery:
    """Search query configuration"""
    query: str
    context: SearchContext
    symbols: Optional[List[str]] = None
    timeframe: str = "1d"  # 1d, 1w, 1m, 3m, 1y
    max_results: int = 10
    language: str = "en"
    region: str = "us"
    site_restrict: Optional[str] = None  # e.g., "site:reuters.com OR site:bloomberg.com"

class AtlasWebSearchService:
    """
    Centralized web search service for A.T.L.A.S. trading system
    
    Features:
    - Google Custom Search API integration
    - Intelligent query generation based on context
    - Result caching and rate limiting
    - Context-aware result processing
    - Deduplication and relevance scoring
    """
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.search_cache: Dict[str, Tuple[List[SearchResult], datetime]] = {}
        self.rate_limiter: Dict[str, datetime] = {}
        self.cache_ttl = timedelta(minutes=30)  # 30-minute cache
        self.rate_limit_delay = 1.0  # 1 second between requests

        # Configuration
        self.google_api_key = settings.GOOGLE_SEARCH_API_KEY
        self.google_cse_id = settings.GOOGLE_SEARCH_ENGINE_ID
        self.bing_api_key = settings.BING_SEARCH_API_KEY
        self.use_grok_search = getattr(settings, 'WEB_SEARCH_USE_GROK', True)

        # Search availability (Grok is primary, Google/Bing as fallback)
        self.grok_available = self.use_grok_search  # Grok has built-in web search
        self.google_available = bool(self.google_api_key and self.google_cse_id)
        self.bing_available = bool(self.bing_api_key)
        self.search_available = self.grok_available or self.google_available or self.bing_available
        
        # Context-specific query templates
        self.query_templates = {
            SearchContext.MARKET_ANALYSIS: "{symbols} market analysis news today earnings revenue",
            SearchContext.NEWS_SENTIMENT: "{symbols} news sentiment market impact today",
            SearchContext.RISK_MANAGEMENT: "{symbols} regulatory risk compliance SEC filing",
            SearchContext.TRADING_SIGNALS: "{symbols} trading signals technical analysis price movement",
            SearchContext.EDUCATION: "{query} trading education examples case study",
            SearchContext.LEE_METHOD: "{symbols} momentum histogram MACD trading pattern",
            SearchContext.PORTFOLIO_ANALYSIS: "{symbols} sector trends portfolio analysis performance",
            SearchContext.AI_CONVERSATION: "{query} market information trading context",
            SearchContext.GENERAL: "{query}"
        }
        
        # Financial news sources (for site restriction)
        self.financial_sources = [
            "reuters.com", "bloomberg.com", "cnbc.com", "marketwatch.com",
            "yahoo.com/finance", "wsj.com", "ft.com", "seekingalpha.com",
            "fool.com", "benzinga.com", "investing.com", "barrons.com"
        ]
        
        logger.info(f"[WEB_SEARCH] Service initialized - Grok: {self.grok_available}, Google: {self.google_available}, Bing: {self.bing_available}")

    async def initialize(self):
        """Initialize the web search service"""
        try:
            if not self.search_available:
                logger.warning("[WEB_SEARCH] No search APIs available - web search disabled")
                return False
                
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={"User-Agent": "A.T.L.A.S. Trading System/1.0"}
            )
            
            logger.info("[WEB_SEARCH] Service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[WEB_SEARCH] Initialization failed: {e}")
            return False

    async def search(self, search_query: SearchQuery) -> List[SearchResult]:
        """
        Perform web search with intelligent query generation and result processing
        """
        try:
            if not self.search_available:
                logger.warning("[WEB_SEARCH] Search not available")
                return []
            
            # Generate cache key
            cache_key = self._generate_cache_key(search_query)
            
            # Check cache
            if cache_key in self.search_cache:
                cached_results, cached_time = self.search_cache[cache_key]
                if datetime.now() - cached_time < self.cache_ttl:
                    logger.debug(f"[WEB_SEARCH] Cache hit for query: {search_query.query[:50]}...")
                    return cached_results
            
            # Rate limiting
            await self._apply_rate_limit()
            
            # Generate optimized search query
            optimized_query = self._generate_optimized_query(search_query)
            
            # Perform search (try Grok first, then Google, then Bing)
            results = []
            if self.grok_available:
                results = await self._search_grok(optimized_query, search_query)
            elif self.google_available:
                results = await self._search_google(optimized_query, search_query)
            elif self.bing_available:
                results = await self._search_bing(optimized_query, search_query)
            
            # Process and score results
            processed_results = self._process_search_results(results, search_query)
            
            # Cache results
            self.search_cache[cache_key] = (processed_results, datetime.now())
            
            logger.info(f"[WEB_SEARCH] Found {len(processed_results)} results for context: {search_query.context.value}")
            return processed_results
            
        except Exception as e:
            logger.error(f"[WEB_SEARCH] Search failed: {e}")
            return []

    def _generate_optimized_query(self, search_query: SearchQuery) -> str:
        """Generate optimized search query based on context"""
        try:
            template = self.query_templates.get(search_query.context, self.query_templates[SearchContext.GENERAL])
            
            # Format query with symbols if provided
            if search_query.symbols and "{symbols}" in template:
                symbols_str = " ".join(search_query.symbols)
                query = template.format(symbols=symbols_str, query=search_query.query)
            else:
                query = template.format(query=search_query.query)
            
            # Add timeframe constraints
            if search_query.timeframe == "1d":
                query += " today OR yesterday"
            elif search_query.timeframe == "1w":
                query += " this week OR last week"
            elif search_query.timeframe == "1m":
                query += " this month"
            
            # Add site restrictions for financial contexts
            if search_query.context in [SearchContext.MARKET_ANALYSIS, SearchContext.NEWS_SENTIMENT, 
                                      SearchContext.TRADING_SIGNALS, SearchContext.PORTFOLIO_ANALYSIS]:
                if not search_query.site_restrict:
                    # Use top financial sources
                    top_sources = self.financial_sources[:6]  # Limit to avoid query length issues
                    site_filter = " OR ".join([f"site:{source}" for source in top_sources])
                    query = f"({query}) AND ({site_filter})"
            
            return query.strip()

        except Exception as e:
            logger.error(f"[WEB_SEARCH] Query optimization failed: {e}")
            return search_query.query

    async def _search_grok(self, query: str, search_query: SearchQuery) -> List[Dict[str, Any]]:
        """Search using Grok's built-in web search capabilities"""
        try:
            # Import Grok integration
            from atlas_grok_integration import grok_client

            if not grok_client or not hasattr(grok_client, 'search_web'):
                logger.warning("[WEB_SEARCH] Grok web search not available")
                return []

            # Use Grok's web search with context-aware prompt
            search_prompt = f"""
            Please search the web for: {query}

            Context: {search_query.context.value}
            Symbols: {search_query.symbols if search_query.symbols else 'None'}
            Timeframe: {search_query.timeframe}

            Return the search results in a structured format with:
            - Title
            - URL
            - Summary/snippet
            - Source domain
            - Relevance to the query

            Focus on recent, credible financial and market sources.
            """

            # Call Grok's web search
            grok_response = await grok_client.search_web(search_prompt)

            # Parse Grok's response into our format
            results = self._parse_grok_search_response(grok_response, search_query)

            logger.info(f"[WEB_SEARCH] Grok search returned {len(results)} results")
            return results

        except Exception as e:
            logger.error(f"[WEB_SEARCH] Grok search failed: {e}")
            return []

    def _parse_grok_search_response(self, grok_response: str, search_query: SearchQuery) -> List[Dict[str, Any]]:
        """Parse Grok's web search response into our standard format"""
        try:
            results = []

            # This is a simplified parser - in practice, you'd want more sophisticated parsing
            # based on how Grok formats its web search results

            # For now, create a mock result structure that represents Grok's web search
            # In a real implementation, you'd parse Grok's actual response format

            if grok_response and len(grok_response) > 50:  # Basic validation
                # Create a representative result from Grok's search
                result = {
                    "title": f"Grok Web Search: {search_query.query}",
                    "snippet": grok_response[:200] + "..." if len(grok_response) > 200 else grok_response,
                    "url": "https://grok.x.ai/search",  # Placeholder URL
                    "source": "grok_web_search",
                    "relevance_score": 8.5,  # High relevance since Grok has context
                    "grok_enhanced": True
                }
                results.append(result)

            return results

        except Exception as e:
            logger.error(f"[WEB_SEARCH] Grok response parsing failed: {e}")
            return []

    async def _search_google(self, query: str, search_query: SearchQuery) -> List[Dict[str, Any]]:
        """Search using Google Custom Search API"""
        try:
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                "key": self.google_api_key,
                "cx": self.google_cse_id,
                "q": query,
                "num": min(search_query.max_results, 10),  # Google max is 10
                "lr": f"lang_{search_query.language}",
                "gl": search_query.region,
                "safe": "medium",
                "dateRestrict": self._get_date_restrict(search_query.timeframe)
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("items", [])
                else:
                    logger.error(f"[WEB_SEARCH] Google API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"[WEB_SEARCH] Google search failed: {e}")
            return []

    async def _search_bing(self, query: str, search_query: SearchQuery) -> List[Dict[str, Any]]:
        """Search using Bing Search API"""
        try:
            url = "https://api.bing.microsoft.com/v7.0/search"
            headers = {"Ocp-Apim-Subscription-Key": self.bing_api_key}
            params = {
                "q": query,
                "count": search_query.max_results,
                "mkt": f"{search_query.language}-{search_query.region}",
                "safeSearch": "Moderate",
                "freshness": self._get_bing_freshness(search_query.timeframe)
            }
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("webPages", {}).get("value", [])
                else:
                    logger.error(f"[WEB_SEARCH] Bing API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"[WEB_SEARCH] Bing search failed: {e}")
            return []

    def _process_search_results(self, raw_results: List[Dict[str, Any]], 
                              search_query: SearchQuery) -> List[SearchResult]:
        """Process and score search results"""
        try:
            processed_results = []
            
            for item in raw_results:
                # Extract data (handle both Google and Bing formats)
                title = item.get("title", "")
                snippet = item.get("snippet", "") or item.get("description", "")
                url = item.get("link", "") or item.get("url", "")
                
                if not title or not url:
                    continue
                
                # Calculate relevance score
                relevance_score = self._calculate_relevance_score(
                    title, snippet, search_query
                )
                
                # Extract published date if available
                published_date = self._extract_published_date(item)
                
                # Determine source
                source = self._extract_source_domain(url)
                
                result = SearchResult(
                    title=title,
                    snippet=snippet,
                    url=url,
                    source=source,
                    published_date=published_date,
                    relevance_score=relevance_score,
                    search_context=search_query.context,
                    metadata={
                        "original_query": search_query.query,
                        "symbols": search_query.symbols,
                        "timeframe": search_query.timeframe,
                        "raw_data": item
                    }
                )
                
                processed_results.append(result)
            
            # Sort by relevance score
            processed_results.sort(key=lambda x: x.relevance_score, reverse=True)
            
            return processed_results[:search_query.max_results]
            
        except Exception as e:
            logger.error(f"[WEB_SEARCH] Result processing failed: {e}")
            return []

    def _calculate_relevance_score(self, title: str, snippet: str,
                                 search_query: SearchQuery) -> float:
        """Calculate relevance score for search result"""
        try:
            score = 0.0
            title_lower = title.lower()
            snippet_lower = snippet.lower()
            query_lower = search_query.query.lower()

            # Query term matches (higher weight for title)
            query_words = query_lower.split()
            for word in query_words:
                if len(word) > 2:  # Skip short words
                    if word in title_lower:
                        score += 2.0
                    if word in snippet_lower:
                        score += 1.0

            # Symbol matches (if provided)
            if search_query.symbols:
                for symbol in search_query.symbols:
                    symbol_lower = symbol.lower()
                    if symbol_lower in title_lower:
                        score += 3.0
                    if symbol_lower in snippet_lower:
                        score += 1.5

            # Context-specific scoring
            context_keywords = self._get_context_keywords(search_query.context)
            for keyword in context_keywords:
                if keyword in title_lower:
                    score += 1.5
                if keyword in snippet_lower:
                    score += 0.5

            # Recency bonus (if we can determine date)
            # This would be enhanced with actual date parsing

            return min(score, 10.0)  # Cap at 10.0

        except Exception as e:
            logger.error(f"[WEB_SEARCH] Relevance scoring failed: {e}")
            return 1.0

    def _get_context_keywords(self, context: SearchContext) -> List[str]:
        """Get context-specific keywords for relevance scoring"""
        keywords_map = {
            SearchContext.MARKET_ANALYSIS: ["market", "analysis", "earnings", "revenue", "forecast"],
            SearchContext.NEWS_SENTIMENT: ["news", "sentiment", "impact", "reaction", "market"],
            SearchContext.RISK_MANAGEMENT: ["risk", "regulatory", "compliance", "SEC", "filing"],
            SearchContext.TRADING_SIGNALS: ["trading", "signals", "technical", "price", "movement"],
            SearchContext.EDUCATION: ["education", "tutorial", "guide", "example", "learn"],
            SearchContext.LEE_METHOD: ["momentum", "histogram", "MACD", "pattern", "signal"],
            SearchContext.PORTFOLIO_ANALYSIS: ["portfolio", "sector", "performance", "analysis"],
            SearchContext.AI_CONVERSATION: ["market", "trading", "financial", "stock"],
            SearchContext.GENERAL: []
        }
        return keywords_map.get(context, [])

    def _extract_published_date(self, item: Dict[str, Any]) -> Optional[datetime]:
        """Extract published date from search result"""
        try:
            # Try different date fields
            date_fields = ["publishedDate", "datePublished", "date", "pubDate"]
            for field in date_fields:
                if field in item and item[field]:
                    # This would need proper date parsing
                    # For now, return None and enhance later
                    pass
            return None
        except Exception:
            return None

    def _extract_source_domain(self, url: str) -> str:
        """Extract source domain from URL"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            # Remove www. prefix
            if domain.startswith("www."):
                domain = domain[4:]
            return domain
        except Exception:
            return "unknown"

    def _get_date_restrict(self, timeframe: str) -> str:
        """Get Google date restriction parameter"""
        date_map = {
            "1d": "d1",
            "1w": "w1",
            "1m": "m1",
            "3m": "m3",
            "1y": "y1"
        }
        return date_map.get(timeframe, "")

    def _get_bing_freshness(self, timeframe: str) -> str:
        """Get Bing freshness parameter"""
        freshness_map = {
            "1d": "Day",
            "1w": "Week",
            "1m": "Month",
            "3m": "",  # No 3-month option
            "1y": ""   # No 1-year option
        }
        return freshness_map.get(timeframe, "")

    def _generate_cache_key(self, search_query: SearchQuery) -> str:
        """Generate cache key for search query"""
        key_data = {
            "query": search_query.query,
            "context": search_query.context.value,
            "symbols": search_query.symbols,
            "timeframe": search_query.timeframe,
            "max_results": search_query.max_results
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()

    async def _apply_rate_limit(self):
        """Apply rate limiting between requests"""
        try:
            now = datetime.now()
            last_request = self.rate_limiter.get("last_request")

            if last_request:
                time_diff = (now - last_request).total_seconds()
                if time_diff < self.rate_limit_delay:
                    sleep_time = self.rate_limit_delay - time_diff
                    await asyncio.sleep(sleep_time)

            self.rate_limiter["last_request"] = now

        except Exception as e:
            logger.error(f"[WEB_SEARCH] Rate limiting failed: {e}")

    async def search_for_context(self, query: str, context: SearchContext,
                               symbols: Optional[List[str]] = None,
                               max_results: int = 5) -> List[SearchResult]:
        """
        Convenience method for context-based searching
        """
        search_query = SearchQuery(
            query=query,
            context=context,
            symbols=symbols,
            max_results=max_results
        )
        return await self.search(search_query)

    async def search_market_news(self, symbols: List[str],
                               timeframe: str = "1d") -> List[SearchResult]:
        """Search for market news about specific symbols"""
        query = f"market news {' '.join(symbols)}"
        search_query = SearchQuery(
            query=query,
            context=SearchContext.MARKET_ANALYSIS,
            symbols=symbols,
            timeframe=timeframe,
            max_results=10
        )
        return await self.search(search_query)

    async def search_trading_context(self, query: str,
                                   symbols: Optional[List[str]] = None) -> List[SearchResult]:
        """Search for trading-related context information"""
        search_query = SearchQuery(
            query=query,
            context=SearchContext.AI_CONVERSATION,
            symbols=symbols,
            max_results=5
        )
        return await self.search(search_query)

    def is_available(self) -> bool:
        """Check if web search is available"""
        return self.search_available

    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            logger.info("[WEB_SEARCH] Service cleaned up")
        except Exception as e:
            logger.error(f"[WEB_SEARCH] Cleanup failed: {e}")

# Global web search service instance
web_search_service = AtlasWebSearchService()
