{"name": "atlas_v4_enhanced", "version": "5.0.0", "description": "A.T.L.A.S v5.0 - Advanced Trading & Learning Analytics System with Modern Chat Interface", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "start": "vite", "atlas:start": "python atlas_server.py", "atlas:dev": "concurrently \"npm run atlas:start\" \"npm run dev\""}, "keywords": ["trading", "ai", "chat", "atlas", "grok", "finance"], "author": "A.T.L.A.S Team", "license": "MIT", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^7.2.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "react": "^18.3.1", "react-dom": "^18.3.1", "stream-chat": "^9.11.0", "stream-chat-react": "^13.2.3", "typescript": "^5.8.3", "vite": "^7.0.5"}}