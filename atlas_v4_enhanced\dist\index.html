<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/atlas-logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>A.T.L.A.S. v5.0 - Advanced Trading & Learning Analytics System</title>
    <meta name="description" content="A.T.L.A.S. v5.0 Enhanced - Modern Chat Interface for Advanced Trading Analytics with Grok AI Integration" />
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Cyberpunk theme variables -->
    <style>
      /* Critical CSS for initial load */
      html, body {
        margin: 0;
        padding: 0;
        height: 100%;
        background: #0a0a0f;
        color: #ffffff;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        overflow: hidden;
      }
      
      #root {
        height: 100vh;
        display: flex;
        flex-direction: column;
      }
      
      /* Loading screen styles */
      .atlas-initial-loading {
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 100%);
        flex-direction: column;
      }
      
      .atlas-initial-logo {
        font-size: 3rem;
        font-weight: 700;
        background: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1rem;
        animation: glow 2s ease-in-out infinite alternate;
      }
      
      .atlas-initial-subtitle {
        color: #8892b0;
        font-size: 1.2rem;
        margin-bottom: 2rem;
        text-align: center;
      }
      
      .atlas-initial-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(0, 255, 136, 0.3);
        border-top: 3px solid #00ff88;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes glow {
        from {
          text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }
        to {
          text-shadow: 0 0 20px rgba(0, 255, 136, 0.8);
        }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide initial loading when React loads */
      .atlas-loaded .atlas-initial-loading {
        display: none;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-CcTQAflf.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-B1pjEr3V.css">
  </head>
  <body>
    <div id="root">
      <!-- Initial loading screen shown before React loads -->
      <div class="atlas-initial-loading">
        <div class="atlas-initial-logo">A.T.L.A.S.</div>
        <div class="atlas-initial-subtitle">
          Advanced Trading & Learning Analytics System v5.0<br>
          Loading Modern Chat Interface...
        </div>
        <div class="atlas-initial-spinner"></div>
      </div>
    </div>
    
    <!-- React application entry point -->
    
    <!-- Mark as loaded when React takes over -->
    <script>
      window.addEventListener('DOMContentLoaded', () => {
        // Add loaded class after a short delay to ensure smooth transition
        setTimeout(() => {
          document.body.classList.add('atlas-loaded');
        }, 100);
      });
    </script>
  </body>
</html>
