var e="-ms-";var r="-moz-";var a="-webkit-";var n="comm";var c="rule";var s="decl";var t="@page";var u="@media";var i="@import";var f="@charset";var o="@viewport";var l="@supports";var v="@document";var p="@namespace";var h="@keyframes";var b="@font-face";var w="@counter-style";var d="@font-feature-values";var $="@layer";var g=Math.abs;var k=String.fromCharCode;var m=Object.assign;function x(e,r){return O(e,0)^45?(((r<<2^O(e,0))<<2^O(e,1))<<2^O(e,2))<<2^O(e,3):0}function y(e){return e.trim()}function j(e,r){return(e=r.exec(e))?e[0]:e}function z(e,r,a){return e.replace(r,a)}function C(e,r){return e.indexOf(r)}function O(e,r){return e.charCodeAt(r)|0}function A(e,r,a){return e.slice(r,a)}function M(e){return e.length}function S(e){return e.length}function q(e,r){return r.push(e),e}function B(e,r){return e.map(r).join("")}var D=1;var E=1;var F=0;var G=0;var H=0;var I="";function J(e,r,a,n,c,s,t){return{value:e,root:r,parent:a,type:n,props:c,children:s,line:D,column:E,length:t,return:""}}function K(e,r){return m(J("",null,null,"",null,null,0),e,{length:-e.length},r)}function L(){return H}function N(){H=G>0?O(I,--G):0;if(E--,H===10)E=1,D--;return H}function P(){H=G<F?O(I,G++):0;if(E++,H===10)E=1,D++;return H}function Q(){return O(I,G)}function R(){return G}function T(e,r){return A(I,e,r)}function U(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function V(e){return D=E=1,F=M(I=e),G=0,[]}function W(e){return I="",e}function X(e){return y(T(G-1,re(e===91?e+2:e===40?e+1:e)))}function Y(e){return W(_(V(e)))}function Z(e){while(H=Q())if(H<33)P();else break;return U(e)>2||U(H)>3?"":" "}function _(e){while(P())switch(U(H)){case 0:q(ne(G-1),e);break;case 2:q(X(H),e);break;default:q(k(H),e)}return e}function ee(e,r){while(--r&&P())if(H<48||H>102||H>57&&H<65||H>70&&H<97)break;return T(e,R()+(r<6&&Q()==32&&P()==32))}function re(e){while(P())switch(H){case e:return G;case 34:case 39:if(e!==34&&e!==39)re(H);break;case 40:if(e===41)re(e);break;case 92:P();break}return G}function ae(e,r){while(P())if(e+H===47+10)break;else if(e+H===42+42&&Q()===47)break;return"/*"+T(r,G-1)+"*"+k(e===47?e:P())}function ne(e){while(!U(Q()))P();return T(e,G)}function ce(e){return W(se("",null,null,null,[""],e=V(e),0,[0],e))}function se(e,r,a,n,c,s,t,u,i){var f=0;var o=0;var l=t;var v=0;var p=0;var h=0;var b=1;var w=1;var d=1;var $=0;var g="";var m=c;var x=s;var y=n;var j=g;while(w)switch(h=$,$=P()){case 40:if(h!=108&&O(j,l-1)==58){if(C(j+=z(X($),"&","&\f"),"&\f")!=-1)d=-1;break}case 34:case 39:case 91:j+=X($);break;case 9:case 10:case 13:case 32:j+=Z(h);break;case 92:j+=ee(R()-1,7);continue;case 47:switch(Q()){case 42:case 47:q(ue(ae(P(),R()),r,a),i);break;default:j+="/"}break;case 123*b:u[f++]=M(j)*d;case 125*b:case 59:case 0:switch($){case 0:case 125:w=0;case 59+o:if(d==-1)j=z(j,/\f/g,"");if(p>0&&M(j)-l)q(p>32?ie(j+";",n,a,l-1):ie(z(j," ","")+";",n,a,l-2),i);break;case 59:j+=";";default:q(y=te(j,r,a,f,o,c,u,g,m=[],x=[],l),s);if($===123)if(o===0)se(j,r,y,y,m,s,l,u,x);else switch(v===99&&O(j,3)===110?100:v){case 100:case 108:case 109:case 115:se(e,y,y,n&&q(te(e,y,y,0,0,c,u,g,c,m=[],l),x),c,x,l,u,n?m:x);break;default:se(j,y,y,y,[""],x,0,u,x)}}f=o=p=0,b=d=1,g=j="",l=t;break;case 58:l=1+M(j),p=h;default:if(b<1)if($==123)--b;else if($==125&&b++==0&&N()==125)continue;switch(j+=k($),$*b){case 38:d=o>0?1:(j+="\f",-1);break;case 44:u[f++]=(M(j)-1)*d,d=1;break;case 64:if(Q()===45)j+=X(P());v=Q(),o=l=M(g=j+=ne(R())),$++;break;case 45:if(h===45&&M(j)==2)b=0}}return s}function te(e,r,a,n,s,t,u,i,f,o,l){var v=s-1;var p=s===0?t:[""];var h=S(p);for(var b=0,w=0,d=0;b<n;++b)for(var $=0,k=A(e,v+1,v=g(w=u[b])),m=e;$<h;++$)if(m=y(w>0?p[$]+" "+k:z(k,/&\f/g,p[$])))f[d++]=m;return J(e,r,a,s===0?c:i,f,o,l)}function ue(e,r,a){return J(e,r,a,n,k(L()),A(e,2,-2),0)}function ie(e,r,a,n){return J(e,r,a,s,A(e,0,n),A(e,n+1,-1),n)}function fe(n,c,s){switch(x(n,c)){case 5103:return a+"print-"+n+n;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return a+n+n;case 4789:return r+n+n;case 5349:case 4246:case 4810:case 6968:case 2756:return a+n+r+n+e+n+n;case 5936:switch(O(n,c+11)){case 114:return a+n+e+z(n,/[svh]\w+-[tblr]{2}/,"tb")+n;case 108:return a+n+e+z(n,/[svh]\w+-[tblr]{2}/,"tb-rl")+n;case 45:return a+n+e+z(n,/[svh]\w+-[tblr]{2}/,"lr")+n}case 6828:case 4268:case 2903:return a+n+e+n+n;case 6165:return a+n+e+"flex-"+n+n;case 5187:return a+n+z(n,/(\w+).+(:[^]+)/,a+"box-$1$2"+e+"flex-$1$2")+n;case 5443:return a+n+e+"flex-item-"+z(n,/flex-|-self/g,"")+(!j(n,/flex-|baseline/)?e+"grid-row-"+z(n,/flex-|-self/g,""):"")+n;case 4675:return a+n+e+"flex-line-pack"+z(n,/align-content|flex-|-self/g,"")+n;case 5548:return a+n+e+z(n,"shrink","negative")+n;case 5292:return a+n+e+z(n,"basis","preferred-size")+n;case 6060:return a+"box-"+z(n,"-grow","")+a+n+e+z(n,"grow","positive")+n;case 4554:return a+z(n,/([^-])(transform)/g,"$1"+a+"$2")+n;case 6187:return z(z(z(n,/(zoom-|grab)/,a+"$1"),/(image-set)/,a+"$1"),n,"")+n;case 5495:case 3959:return z(n,/(image-set\([^]*)/,a+"$1"+"$`$1");case 4968:return z(z(n,/(.+:)(flex-)?(.*)/,a+"box-pack:$3"+e+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+a+n+n;case 4200:if(!j(n,/flex-|baseline/))return e+"grid-column-align"+A(n,c)+n;break;case 2592:case 3360:return e+z(n,"template-","")+n;case 4384:case 3616:if(s&&s.some((function(e,r){return c=r,j(e.props,/grid-\w+-end/)}))){return~C(n+(s=s[c].value),"span")?n:e+z(n,"-start","")+n+e+"grid-row-span:"+(~C(s,"span")?j(s,/\d+/):+j(s,/\d+/)-+j(n,/\d+/))+";"}return e+z(n,"-start","")+n;case 4896:case 4128:return s&&s.some((function(e){return j(e.props,/grid-\w+-start/)}))?n:e+z(z(n,"-end","-span"),"span ","")+n;case 4095:case 3583:case 4068:case 2532:return z(n,/(.+)-inline(.+)/,a+"$1$2")+n;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(M(n)-1-c>6)switch(O(n,c+1)){case 109:if(O(n,c+4)!==45)break;case 102:return z(n,/(.+:)(.+)-([^]+)/,"$1"+a+"$2-$3"+"$1"+r+(O(n,c+3)==108?"$3":"$2-$3"))+n;case 115:return~C(n,"stretch")?fe(z(n,"stretch","fill-available"),c,s)+n:n}break;case 5152:case 5920:return z(n,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,(function(r,a,c,s,t,u,i){return e+a+":"+c+i+(s?e+a+"-span:"+(t?u:+u-+c)+i:"")+n}));case 4949:if(O(n,c+6)===121)return z(n,":",":"+a)+n;break;case 6444:switch(O(n,O(n,14)===45?18:11)){case 120:return z(n,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+a+(O(n,14)===45?"inline-":"")+"box$3"+"$1"+a+"$2$3"+"$1"+e+"$2box$3")+n;case 100:return z(n,":",":"+e)+n}break;case 5719:case 2647:case 2135:case 3927:case 2391:return z(n,"scroll-","scroll-snap-")+n}return n}function oe(e,r){var a="";var n=S(e);for(var c=0;c<n;c++)a+=r(e[c],c,e,r)||"";return a}function le(e,r,a,t){switch(e.type){case $:if(e.children.length)break;case i:case s:return e.return=e.return||e.value;case n:return"";case h:return e.return=e.value+"{"+oe(e.children,t)+"}";case c:e.value=e.props.join(",")}return M(a=oe(e.children,t))?e.return=e.value+"{"+a+"}":""}function ve(e){var r=S(e);return function(a,n,c,s){var t="";for(var u=0;u<r;u++)t+=e[u](a,n,c,s)||"";return t}}function pe(e){return function(r){if(!r.root)if(r=r.return)e(r)}}function he(n,t,u,i){if(n.length>-1)if(!n.return)switch(n.type){case s:n.return=fe(n.value,n.length,u);return;case h:return oe([K(n,{value:z(n.value,"@","@"+a)})],i);case c:if(n.length)return B(n.props,(function(c){switch(j(c,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return oe([K(n,{props:[z(c,/:(read-\w+)/,":"+r+"$1")]})],i);case"::placeholder":return oe([K(n,{props:[z(c,/:(plac\w+)/,":"+a+"input-$1")]}),K(n,{props:[z(c,/:(plac\w+)/,":"+r+"$1")]}),K(n,{props:[z(c,/:(plac\w+)/,e+"input-$1")]})],i)}return""}))}}function be(e){switch(e.type){case c:e.props=e.props.map((function(r){return B(Y(r),(function(r,a,n){switch(O(r,0)){case 12:return A(r,1,M(r));case 0:case 40:case 43:case 62:case 126:return r;case 58:if(n[++a]==="global")n[a]="",n[++a]="\f"+A(n[a],a=1,-1);case 32:return a===1?"":r;default:switch(a){case 0:e=r;return S(n)>1?"":r;case a=S(n)-1:case 2:return a===2?r+e+e:r+e;default:return r}}}))}))}}export{f as CHARSET,n as COMMENT,w as COUNTER_STYLE,s as DECLARATION,v as DOCUMENT,b as FONT_FACE,d as FONT_FEATURE_VALUES,i as IMPORT,h as KEYFRAMES,$ as LAYER,u as MEDIA,r as MOZ,e as MS,p as NAMESPACE,t as PAGE,c as RULESET,l as SUPPORTS,o as VIEWPORT,a as WEBKIT,g as abs,V as alloc,q as append,m as assign,R as caret,L as char,H as character,I as characters,O as charat,E as column,B as combine,ue as comment,ae as commenter,ce as compile,K as copy,W as dealloc,ie as declaration,X as delimit,re as delimiter,ee as escaping,k as from,x as hash,ne as identifier,C as indexof,F as length,D as line,j as match,ve as middleware,be as namespace,P as next,J as node,se as parse,Q as peek,G as position,fe as prefix,he as prefixer,N as prev,z as replace,te as ruleset,pe as rulesheet,oe as serialize,S as sizeof,T as slice,le as stringify,M as strlen,A as substr,U as token,Y as tokenize,_ as tokenizer,y as trim,Z as whitespace};
//# sourceMappingURL=stylis.mjs.map
