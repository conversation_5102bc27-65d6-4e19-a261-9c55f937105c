import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  LinearProgress,
  Grid,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Psychology as PsychologyIcon,
  Speed as SpeedIcon,
  Storage as StorageIcon,
  Wifi as WifiIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

import { SystemStatus } from '../../types/atlas';

const StatusContainer = styled(Box)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  background: 'var(--atlas-bg-secondary)',
  overflowY: 'auto',
}));

const StatusHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  borderBottom: '1px solid var(--atlas-border-primary)',
  background: 'var(--atlas-bg-tertiary)',
}));

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  background: 'transparent',
  border: 'none',
  boxShadow: 'none',
  '&:before': {
    display: 'none',
  },
  '& .MuiAccordionSummary-root': {
    backgroundColor: 'var(--atlas-bg-tertiary)',
    borderBottom: '1px solid var(--atlas-border-primary)',
    minHeight: '56px',
    '&:hover': {
      backgroundColor: 'var(--atlas-bg-hover)',
    },
  },
  '& .MuiAccordionDetails-root': {
    backgroundColor: 'var(--atlas-bg-primary)',
    borderBottom: '1px solid var(--atlas-border-primary)',
  },
}));

const MetricCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  background: 'var(--atlas-gradient-card)',
  border: '1px solid var(--atlas-border-primary)',
  borderRadius: theme.spacing(1),
  textAlign: 'center',
}));

const MetricValue = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 700,
  color: 'var(--atlas-primary)',
  fontFamily: 'var(--atlas-font-mono)',
}));

interface AtlasStatusPanelProps {
  systemStatus?: SystemStatus;
  healthStatus?: SystemStatus | null;
  performanceMetrics?: any;
  isSystemHealthy: boolean;
}

const AtlasStatusPanel: React.FC<AtlasStatusPanelProps> = ({
  systemStatus,
  healthStatus,
  performanceMetrics: _performanceMetrics,
  isSystemHealthy,
}) => {
  const [expandedPanels, setExpandedPanels] = useState<string[]>(['system-health']);

  const handleAccordionChange = (panel: string) => (
    _event: React.SyntheticEvent,
    isExpanded: boolean
  ) => {
    setExpandedPanels(prev => 
      isExpanded 
        ? [...prev, panel]
        : prev.filter(p => p !== panel)
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
      case 'active':
      case 'available':
        return '#00ff88';
      case 'degraded':
      case 'fallback':
        return '#ffaa00';
      case 'unhealthy':
      case 'disconnected':
      case 'inactive':
      case 'unavailable':
      case 'error':
        return '#ff3366';
      default:
        return '#8892b0';
    }
  };

  const getStatusIcon = (category: string) => {
    switch (category) {
      case 'system-health':
        return <SpeedIcon />;
      case 'grok-ai':
        return <PsychologyIcon />;
      case 'database':
        return <StorageIcon />;
      case 'connectivity':
        return <WifiIcon />;
      case 'performance':
        return <TrendingUpIcon />;
      case 'security':
        return <SecurityIcon />;
      default:
        return <SpeedIcon />;
    }
  };

  return (
    <StatusContainer>
      {/* Header */}
      <StatusHeader>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography
              variant="h6"
              sx={{
                color: 'var(--atlas-primary)',
                fontWeight: 600,
                marginBottom: 1,
              }}
            >
              System Status
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: 'var(--atlas-text-secondary)',
              }}
            >
              Real-time Monitoring
            </Typography>
          </Box>
          <Tooltip title="Refresh Status">
            <IconButton
              sx={{
                backgroundColor: 'var(--atlas-primary)',
                color: 'var(--atlas-bg-primary)',
                '&:hover': {
                  backgroundColor: 'var(--atlas-primary-dark)',
                  boxShadow: 'var(--atlas-glow-primary)',
                },
              }}
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Overall Health Indicator */}
        <Box sx={{ marginTop: 2 }}>
          <Chip
            icon={<SpeedIcon />}
            label={isSystemHealthy ? 'System Healthy' : 'System Issues'}
            sx={{
              backgroundColor: isSystemHealthy 
                ? 'rgba(0, 255, 136, 0.1)' 
                : 'rgba(255, 51, 102, 0.1)',
              color: isSystemHealthy ? '#00ff88' : '#ff3366',
              border: `1px solid ${isSystemHealthy 
                ? 'rgba(0, 255, 136, 0.3)' 
                : 'rgba(255, 51, 102, 0.3)'}`,
            }}
          />
        </Box>
      </StatusHeader>

      {/* System Health */}
      <StyledAccordion
        expanded={expandedPanels.includes('system-health')}
        onChange={handleAccordionChange('system-health')}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon sx={{ color: 'var(--atlas-text-secondary)' }} />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {getStatusIcon('system-health')}
            <Typography sx={{ color: 'var(--atlas-text-primary)' }}>
              System Health
            </Typography>
            <Chip
              label={healthStatus?.backend_health || 'Unknown'}
              size="small"
              sx={{
                backgroundColor: `${getStatusColor(healthStatus?.backend_health || 'unknown')}20`,
                color: getStatusColor(healthStatus?.backend_health || 'unknown'),
                border: `1px solid ${getStatusColor(healthStatus?.backend_health || 'unknown')}50`,
              }}
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <MetricCard>
                <MetricValue>{healthStatus?.api_endpoints || 0}</MetricValue>
                <Typography variant="caption" color="textSecondary">
                  API Endpoints
                </Typography>
              </MetricCard>
            </Grid>
            <Grid item xs={6}>
              <MetricCard>
                <MetricValue>{healthStatus?.websocket_connections || 0}</MetricValue>
                <Typography variant="caption" color="textSecondary">
                  WebSocket Connections
                </Typography>
              </MetricCard>
            </Grid>
          </Grid>
        </AccordionDetails>
      </StyledAccordion>

      {/* Grok AI Status */}
      <StyledAccordion
        expanded={expandedPanels.includes('grok-ai')}
        onChange={handleAccordionChange('grok-ai')}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon sx={{ color: 'var(--atlas-text-secondary)' }} />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {getStatusIcon('grok-ai')}
            <Typography sx={{ color: 'var(--atlas-text-primary)' }}>
              Grok AI Integration
            </Typography>
            <Chip
              label={systemStatus?.grok_status || 'Unknown'}
              size="small"
              sx={{
                backgroundColor: `${getStatusColor(systemStatus?.grok_status || 'unknown')}20`,
                color: getStatusColor(systemStatus?.grok_status || 'unknown'),
                border: `1px solid ${getStatusColor(systemStatus?.grok_status || 'unknown')}50`,
              }}
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="body2" color="textSecondary" gutterBottom>
            Enhanced reasoning, vision capabilities, and real-time search integration
          </Typography>
          <Box sx={{ marginTop: 2 }}>
            <Typography variant="caption" color="textSecondary">
              Fallback Chain: Grok → OpenAI → Static
            </Typography>
            <LinearProgress
              variant="determinate"
              value={systemStatus?.grok_status === 'available' ? 100 : 50}
              sx={{
                marginTop: 1,
                backgroundColor: 'rgba(0, 255, 136, 0.1)',
                '& .MuiLinearProgress-bar': {
                  background: 'var(--atlas-gradient-primary)',
                },
              }}
            />
          </Box>
        </AccordionDetails>
      </StyledAccordion>

      {/* Database Status */}
      <StyledAccordion
        expanded={expandedPanels.includes('database')}
        onChange={handleAccordionChange('database')}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon sx={{ color: 'var(--atlas-text-secondary)' }} />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {getStatusIcon('database')}
            <Typography sx={{ color: 'var(--atlas-text-primary)' }}>
              Database Systems
            </Typography>
            <Chip
              label={systemStatus?.database_status || 'Unknown'}
              size="small"
              sx={{
                backgroundColor: `${getStatusColor(systemStatus?.database_status || 'unknown')}20`,
                color: getStatusColor(systemStatus?.database_status || 'unknown'),
                border: `1px solid ${getStatusColor(systemStatus?.database_status || 'unknown')}50`,
              }}
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="body2" color="textSecondary" gutterBottom>
            Multi-database architecture with specialized storage
          </Typography>
          <Box sx={{ marginTop: 2 }}>
            {[
              'Main DB - Trading Data',
              'Memory DB - Conversations',
              'RAG DB - Knowledge Base',
              'Compliance DB - Audit Trails',
              'Feedback DB - Analytics'
            ].map((db) => (
              <Box key={db} sx={{ display: 'flex', justifyContent: 'space-between', marginBottom: 1 }}>
                <Typography variant="caption" color="textSecondary">
                  {db}
                </Typography>
                <Chip
                  label="Connected"
                  size="small"
                  sx={{
                    backgroundColor: 'rgba(0, 255, 136, 0.1)',
                    color: '#00ff88',
                    border: '1px solid rgba(0, 255, 136, 0.3)',
                    height: '20px',
                    fontSize: '0.6rem',
                  }}
                />
              </Box>
            ))}
          </Box>
        </AccordionDetails>
      </StyledAccordion>

      {/* Performance Metrics */}
      <StyledAccordion
        expanded={expandedPanels.includes('performance')}
        onChange={handleAccordionChange('performance')}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon sx={{ color: 'var(--atlas-text-secondary)' }} />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {getStatusIcon('performance')}
            <Typography sx={{ color: 'var(--atlas-text-primary)' }}>
              Performance Metrics
            </Typography>
            <Chip
              label="35%+ Returns"
              size="small"
              sx={{
                backgroundColor: 'rgba(0, 255, 136, 0.1)',
                color: '#00ff88',
                border: '1px solid rgba(0, 255, 136, 0.3)',
              }}
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <MetricCard>
                <MetricValue>87%</MetricValue>
                <Typography variant="caption" color="textSecondary">
                  Signal Accuracy
                </Typography>
              </MetricCard>
            </Grid>
            <Grid item xs={6}>
              <MetricCard>
                <MetricValue>1.2s</MetricValue>
                <Typography variant="caption" color="textSecondary">
                  Avg Response Time
                </Typography>
              </MetricCard>
            </Grid>
            <Grid item xs={6}>
              <MetricCard>
                <MetricValue>99.9%</MetricValue>
                <Typography variant="caption" color="textSecondary">
                  Uptime
                </Typography>
              </MetricCard>
            </Grid>
            <Grid item xs={6}>
              <MetricCard>
                <MetricValue>24/7</MetricValue>
                <Typography variant="caption" color="textSecondary">
                  Monitoring
                </Typography>
              </MetricCard>
            </Grid>
          </Grid>
        </AccordionDetails>
      </StyledAccordion>
    </StatusContainer>
  );
};

export default AtlasStatusPanel;
