import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  TextField,
  IconButton,
  Typography,
  Paper,
  Fade,
  Chip,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,

  Badge,
  Tooltip,
} from '@mui/material';
import {
  Send as SendIcon,
  Mic as MicIcon,
  AttachFile as AttachFileIcon,
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon,
  ExpandMore as ExpandMoreIcon,
  Article as ArticleIcon,
  Search as SearchIcon,
  Analytics as AnalyticsIcon,
  AutoAwesome as AutoAwesomeIcon,
  Insights as InsightsIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// A.T.L.A.S. Components (will be created)
// import AtlasChatMessage from './AtlasChatMessage';
// import AtlasTypingIndicator from './AtlasTypingIndicator';
// import AtlasQuickActions from './AtlasQuickActions';

// Services and Types
import { atlasApi } from '../../services/atlasApi';
import { AtlasMessage, ProgressUpdate } from '../../types/atlas';
import { useAtlasWebSocket } from '../../hooks/useAtlasWebSocket';
import { useAtlasSystem } from '../../hooks/useAtlasSystem';

const ChatContainer = styled(Box)(() => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  background: 'var(--atlas-bg-primary)',
  position: 'relative',
}));

const MessagesContainer = styled(Box)(({ theme }) => ({
  flex: 1,
  overflowY: 'auto',
  padding: theme.spacing(2),
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'var(--atlas-bg-secondary)',
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    background: 'var(--atlas-primary)',
    borderRadius: '4px',
    boxShadow: 'var(--atlas-glow-primary)',
  },
}));

const InputContainer = styled(Paper)(({ theme }) => ({
  margin: theme.spacing(2),
  padding: theme.spacing(2),
  background: 'var(--atlas-gradient-card)',
  border: '1px solid var(--atlas-border-primary)',
  borderRadius: theme.spacing(2),
  backdropFilter: 'blur(10px)',
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    backgroundColor: 'transparent',
    border: '1px solid var(--atlas-border-secondary)',
    borderRadius: theme.spacing(1),
    transition: 'all 0.3s ease',
    '&:hover': {
      border: '1px solid var(--atlas-border-accent)',
      boxShadow: 'var(--atlas-border-glow)',
    },
    '&.Mui-focused': {
      border: '1px solid var(--atlas-primary)',
      boxShadow: 'var(--atlas-glow-primary)',
    },
    '& fieldset': {
      border: 'none',
    },
  },
  '& .MuiInputBase-input': {
    color: 'var(--atlas-text-primary)',
    fontSize: '1rem',
    '&::placeholder': {
      color: 'var(--atlas-text-muted)',
      opacity: 1,
    },
  },
}));

const SendButton = styled(IconButton)(() => ({
  background: 'var(--atlas-gradient-primary)',
  color: 'var(--atlas-bg-primary)',
  width: '48px',
  height: '48px',
  boxShadow: 'var(--atlas-glow-primary)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 0 25px rgba(0, 255, 136, 0.5)',
  },
  '&:disabled': {
    background: 'var(--atlas-bg-tertiary)',
    color: 'var(--atlas-text-muted)',
    boxShadow: 'none',
  },
}));

const ProgressContainer = styled(Box)(() => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  zIndex: 10,
}));

// Enhanced message rendering component
const EnhancedMessageDisplay: React.FC<{ message: AtlasMessage }> = ({ message }) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  // Parse enhanced AI response data
  const parseEnhancedResponse = (_content: string, metadata: any) => {
    const sections = {
      news: metadata?.news_insights || [],
      search: metadata?.web_search_results || [],
      reasoning: metadata?.causal_reasoning || null,
      sentiment: metadata?.sentiment_analysis || null,
      technical: metadata?.technical_analysis || null,
    };
    return sections;
  };

  const sections = parseEnhancedResponse(message.content, message.metadata);

  return (
    <Box>
      {/* Main message content */}
      <Typography variant="body1" sx={{ marginBottom: 2, whiteSpace: 'pre-wrap' }}>
        {message.content}
      </Typography>

      {/* Enhanced AI Features */}
      {message.metadata?.grok_enhanced && (
        <Box sx={{ marginTop: 2 }}>
          {/* News Insights */}
          {sections.news.length > 0 && (
            <Accordion expanded={expandedSections.has('news')} onChange={() => toggleSection('news')}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ArticleIcon color="primary" />
                  <Typography variant="subtitle2">
                    News Insights ({sections.news.length})
                  </Typography>
                  <Badge badgeContent="AI" color="primary" />
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <List dense>
                  {sections.news.slice(0, 5).map((article: any, index: number) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <InsightsIcon fontSize="small" />
                      </ListItemIcon>
                      <ListItemText
                        primary={article.title}
                        secondary={`${article.source} • ${article.sentiment || 'Neutral'} sentiment`}
                      />
                    </ListItem>
                  ))}
                </List>
              </AccordionDetails>
            </Accordion>
          )}

          {/* Web Search Results */}
          {sections.search.length > 0 && (
            <Accordion expanded={expandedSections.has('search')} onChange={() => toggleSection('search')}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SearchIcon color="primary" />
                  <Typography variant="subtitle2">
                    Web Search Results ({sections.search.length})
                  </Typography>
                  <Badge badgeContent="Live" color="success" />
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <List dense>
                  {sections.search.slice(0, 5).map((result: any, index: number) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <SearchIcon fontSize="small" />
                      </ListItemIcon>
                      <ListItemText
                        primary={result.title}
                        secondary={`${result.source} • Relevance: ${Math.round((result.relevance_score || 0.5) * 100)}%`}
                      />
                    </ListItem>
                  ))}
                </List>
              </AccordionDetails>
            </Accordion>
          )}

          {/* Advanced Reasoning */}
          {sections.reasoning && (
            <Accordion expanded={expandedSections.has('reasoning')} onChange={() => toggleSection('reasoning')}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AnalyticsIcon color="primary" />
                  <Typography variant="subtitle2">Causal Reasoning</Typography>
                  <Badge badgeContent="Grok" color="secondary" />
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body2" sx={{ marginBottom: 1 }}>
                  <strong>Confidence:</strong> {Math.round((sections.reasoning.confidence || 0.5) * 100)}%
                </Typography>
                <Typography variant="body2">
                  {sections.reasoning.explanation || 'Advanced causal analysis performed'}
                </Typography>
              </AccordionDetails>
            </Accordion>
          )}

          {/* Sentiment Analysis */}
          {sections.sentiment && (
            <Accordion expanded={expandedSections.has('sentiment')} onChange={() => toggleSection('sentiment')}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {sections.sentiment.score > 0.1 ? <TrendingUpIcon color="success" /> :
                   sections.sentiment.score < -0.1 ? <TrendingDownIcon color="error" /> :
                   <AnalyticsIcon color="primary" />}
                  <Typography variant="subtitle2">Sentiment Analysis</Typography>
                  <Badge badgeContent="AI" color="primary" />
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body2" sx={{ marginBottom: 1 }}>
                  <strong>Score:</strong> {sections.sentiment.score?.toFixed(3) || 'N/A'}
                  ({sections.sentiment.label || 'Neutral'})
                </Typography>
                <Typography variant="body2">
                  <strong>Confidence:</strong> {Math.round((sections.sentiment.confidence || 0.5) * 100)}%
                </Typography>
              </AccordionDetails>
            </Accordion>
          )}
        </Box>
      )}
    </Box>
  );
};

const AtlasChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<AtlasMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentProgress] = useState<ProgressUpdate | null>(null);
  const [sessionId] = useState(() => `atlas-session-${Date.now()}`);
  const [enhancedFeaturesEnabled, setEnhancedFeaturesEnabled] = useState(true);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // WebSocket connection for progress updates
  const { connectProgress } = useAtlasWebSocket();

  // System status for real-time indicators
  const { grokStatus, isSystemHealthy } = useAtlasSystem();

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // Initialize progress WebSocket
  useEffect(() => {
    connectProgress(sessionId);
  }, [sessionId, connectProgress]);

  // Handle progress updates (placeholder for future use)
  // const _handleProgressUpdate = useCallback((update: ProgressUpdate) => {
  //   setCurrentProgress(update);
  //
  //   if (update.status === 'completed' || update.status === 'failed') {
  //     setTimeout(() => {
  //       setCurrentProgress(null);
  //       setIsProcessing(false);
  //     }, 1000);
  //   }
  // }, []);

  // Send message to A.T.L.A.S. with enhanced AI features
  const sendMessage = useCallback(async () => {
    if (!inputValue.trim() || isProcessing) return;

    const userMessage: AtlasMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);
    setIsProcessing(true);

    try {
      // Enhanced API call with AI features request
      const enhancedRequest = {
        message: userMessage.content,
        session_id: sessionId,
        enhanced_features: enhancedFeaturesEnabled,
        request_features: {
          news_insights: true,
          web_search: true,
          causal_reasoning: true,
          sentiment_analysis: true,
          grok_enhancement: true
        }
      };

      const response = await atlasApi.chatEnhanced ?
        await atlasApi.chatEnhanced(enhancedRequest) :
        await atlasApi.chat(userMessage.content, sessionId);

      if (response.success) {
        const assistantMessage: AtlasMessage = {
          id: `assistant-${Date.now()}`,
          type: 'assistant',
          content: response.data.response,
          timestamp: new Date(),
          metadata: {
            confidence: response.data.confidence,
            grok_enhanced: response.data.grok_enhanced,
            news_insights: response.data.news_insights || [],
            web_search_results: response.data.web_search_results || [],
            causal_reasoning: response.data.causal_reasoning || null,
            sentiment_analysis: response.data.sentiment_analysis || null,
            technical_analysis: response.data.technical_analysis || null,
            progress_stage: 'completed',
          },
        };

        setMessages(prev => [...prev, assistantMessage]);
      } else {
        throw new Error(response.error || 'Failed to get response');
      }
    } catch (error) {
      console.error('❌ Chat error:', error);

      const errorMessage: AtlasMessage = {
        id: `error-${Date.now()}`,
        type: 'system',
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
      setIsProcessing(false);
    }
  }, [inputValue, isProcessing, sessionId, enhancedFeaturesEnabled]);

  // Handle Enter key press
  const handleKeyPress = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  }, [sendMessage]);

  // Handle quick action selection
  const handleQuickAction = useCallback((action: string) => {
    setInputValue(action);
    inputRef.current?.focus();
  }, []);

  // Welcome message on first load
  useEffect(() => {
    const grokStatusText = grokStatus?.available ? "✅ **Grok AI Active**" : "🔄 **Grok AI Initializing**";
    const systemStatusText = isSystemHealthy ? "✅ **System Healthy**" : "🔄 **System Initializing**";

    const welcomeMessage: AtlasMessage = {
      id: 'welcome',
      type: 'assistant',
      content: `🚀 **Welcome to A.T.L.A.S. v5.0 Enhanced!**

${grokStatusText} | ${systemStatusText}

I'm your advanced trading assistant with Grok AI integration and enhanced capabilities:

🎯 **Core Trading Features:**
• **Trading Analysis** - 6-point Stock Market God format with Grok AI enhancement
• **Lee Method Scanning** - Ultra-responsive real-time pattern detection
• **Portfolio Management** - Advanced risk assessment and optimization
• **Options Strategies** - Black-Scholes pricing with Greeks analysis

🤖 **Enhanced AI Capabilities:**
• **News Insights** - Real-time sentiment analysis from multiple sources
• **Web Search** - Live market research with relevance scoring
• **Causal Reasoning** - Advanced market relationship analysis
• **Theory of Mind** - Multi-participant market psychology modeling

**Try these enhanced commands:**
- "Analyze AAPL with Grok AI and latest news"
- "Web search: latest Tesla earnings sentiment"
- "Causal analysis: how does Fed policy affect tech stocks?"
- "Theory of mind: what are institutional investors thinking about crypto?"

What would you like to explore today?`,
      timestamp: new Date(),
      metadata: {
        grok_enhanced: grokStatus?.available || false,
      },
    };

    setMessages([welcomeMessage]);
  }, [grokStatus, isSystemHealthy]);

  return (
    <ChatContainer>
      {/* Progress Indicator */}
      {currentProgress && (
        <ProgressContainer>
          <LinearProgress
            variant="determinate"
            value={currentProgress.percentage}
            sx={{
              height: '4px',
              backgroundColor: 'rgba(0, 255, 136, 0.1)',
              '& .MuiLinearProgress-bar': {
                background: 'var(--atlas-gradient-primary)',
              },
            }}
          />
          <Box sx={{ padding: 1, backgroundColor: 'var(--atlas-bg-secondary)' }}>
            <Typography variant="caption" color="primary">
              {currentProgress.message} ({currentProgress.percentage}%)
            </Typography>
          </Box>
        </ProgressContainer>
      )}

      {/* Messages Area */}
      <MessagesContainer>
        {messages.map((message) => (
          <Fade in key={message.id} timeout={300}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',
                marginBottom: 2,
              }}
            >
              <Paper
                sx={{
                  maxWidth: '85%',
                  padding: 2,
                  background: message.type === 'user'
                    ? 'var(--atlas-gradient-primary)'
                    : 'var(--atlas-gradient-card)',
                  color: message.type === 'user'
                    ? 'var(--atlas-bg-primary)'
                    : 'var(--atlas-text-primary)',
                  border: '1px solid var(--atlas-border-primary)',
                  borderRadius: message.type === 'user' ? '20px 20px 4px 20px' : '20px 20px 20px 4px',
                  boxShadow: message.type === 'user'
                    ? 'var(--atlas-glow-primary)'
                    : 'none',
                }}
              >
                {/* Enhanced message display for assistant messages */}
                {message.type === 'assistant' && enhancedFeaturesEnabled ? (
                  <EnhancedMessageDisplay message={message} />
                ) : (
                  <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                    {message.content}
                  </Typography>
                )}

                {/* Enhanced metadata display */}
                {message.metadata && (
                  <Box sx={{ marginTop: 1, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {message.metadata.confidence && (
                      <Chip
                        label={`${Math.round(message.metadata.confidence * 100)}% confidence`}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.7rem' }}
                      />
                    )}
                    {message.metadata.grok_enhanced && (
                      <Chip
                        icon={<AutoAwesomeIcon />}
                        label="Grok Enhanced"
                        size="small"
                        color="primary"
                        sx={{ fontSize: '0.7rem' }}
                      />
                    )}
                    {message.metadata.news_insights && (
                      <Chip
                        icon={<ArticleIcon />}
                        label={`${message.metadata.news_insights.length} News`}
                        size="small"
                        color="info"
                        sx={{ fontSize: '0.7rem' }}
                      />
                    )}
                    {message.metadata.web_search_results && (
                      <Chip
                        icon={<SearchIcon />}
                        label={`${message.metadata.web_search_results.length} Results`}
                        size="small"
                        color="success"
                        sx={{ fontSize: '0.7rem' }}
                      />
                    )}
                  </Box>
                )}

                <Typography
                  variant="caption"
                  sx={{
                    display: 'block',
                    marginTop: 1,
                    opacity: 0.7,
                    fontSize: '0.7rem'
                  }}
                >
                  {message.timestamp.toLocaleTimeString()}
                </Typography>
              </Paper>
            </Box>
          </Fade>
        ))}

        {isTyping && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-start', marginBottom: 2 }}>
            <Paper
              sx={{
                padding: 2,
                background: 'var(--atlas-gradient-card)',
                border: '1px solid var(--atlas-border-primary)',
                borderRadius: '20px 20px 20px 4px',
              }}
            >
              <Typography variant="body2" color="primary" className="atlas-pulse">
                A.T.L.A.S. is thinking...
              </Typography>
            </Paper>
          </Box>
        )}
        
        <div ref={messagesEndRef} />
      </MessagesContainer>

      {/* Enhanced Quick Actions */}
      <Box sx={{ padding: 2, borderTop: '1px solid var(--atlas-border-primary)' }}>
        {/* AI Features Toggle */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 2 }}>
          <Typography variant="subtitle2" color="primary">
            Quick Actions
          </Typography>
          <Tooltip title="Toggle enhanced AI features display">
            <Chip
              icon={<AutoAwesomeIcon />}
              label={enhancedFeaturesEnabled ? "Enhanced AI: ON" : "Enhanced AI: OFF"}
              onClick={() => setEnhancedFeaturesEnabled(!enhancedFeaturesEnabled)}
              color={enhancedFeaturesEnabled ? "primary" : "default"}
              size="small"
              sx={{ cursor: 'pointer' }}
            />
          </Tooltip>
        </Box>

        {/* Action Categories */}
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', marginBottom: 1 }}>
          <Typography variant="caption" color="textSecondary" sx={{ alignSelf: 'center', marginRight: 1 }}>
            Trading:
          </Typography>
          {[
            'Analyze AAPL with Grok AI',
            'Lee Method signals',
            'Portfolio analysis'
          ].map((action) => (
            <Chip
              key={action}
              label={action}
              onClick={() => handleQuickAction(action)}
              size="small"
              sx={{
                backgroundColor: 'rgba(0, 255, 136, 0.1)',
                color: '#00ff88',
                border: '1px solid rgba(0, 255, 136, 0.3)',
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: 'rgba(0, 255, 136, 0.2)',
                  boxShadow: 'var(--atlas-border-glow)',
                },
              }}
            />
          ))}
        </Box>

        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', marginBottom: 1 }}>
          <Typography variant="caption" color="textSecondary" sx={{ alignSelf: 'center', marginRight: 1 }}>
            AI Research:
          </Typography>
          {[
            'Latest market news with sentiment',
            'Web search: tech earnings',
            'Causal analysis: market trends'
          ].map((action) => (
            <Chip
              key={action}
              label={action}
              onClick={() => handleQuickAction(action)}
              size="small"
              sx={{
                backgroundColor: 'rgba(0, 153, 255, 0.1)',
                color: '#0099ff',
                border: '1px solid rgba(0, 153, 255, 0.3)',
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: 'rgba(0, 153, 255, 0.2)',
                  boxShadow: 'var(--atlas-border-glow)',
                },
              }}
            />
          ))}
        </Box>

        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Typography variant="caption" color="textSecondary" sx={{ alignSelf: 'center', marginRight: 1 }}>
            Advanced:
          </Typography>
          {[
            'Options strategies',
            'Risk assessment',
            'Theory of mind analysis'
          ].map((action) => (
            <Chip
              key={action}
              label={action}
              onClick={() => handleQuickAction(action)}
              size="small"
              sx={{
                backgroundColor: 'rgba(255, 170, 0, 0.1)',
                color: '#ffaa00',
                border: '1px solid rgba(255, 170, 0, 0.3)',
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: 'rgba(255, 170, 0, 0.2)',
                  boxShadow: 'var(--atlas-border-glow)',
                },
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Input Area */}
      <InputContainer elevation={0}>
        <Box sx={{ display: 'flex', alignItems: 'flex-end', gap: 2 }}>
          {/* Attachment Button */}
          <IconButton
            sx={{ color: 'var(--atlas-text-secondary)' }}
            disabled={isProcessing}
          >
            <AttachFileIcon />
          </IconButton>

          {/* Voice Input Button */}
          <IconButton
            sx={{ color: 'var(--atlas-text-secondary)' }}
            disabled={isProcessing}
          >
            <MicIcon />
          </IconButton>

          {/* Text Input */}
          <StyledTextField
            ref={inputRef}
            fullWidth
            multiline
            maxRows={4}
            placeholder="Ask A.T.L.A.S. anything about trading, markets, or analysis..."
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isProcessing}
            variant="outlined"
          />

          {/* Send Button */}
          <SendButton
            onClick={sendMessage}
            disabled={!inputValue.trim() || isProcessing}
          >
            <SendIcon />
          </SendButton>
        </Box>

        {/* Status Indicators */}
        <Box sx={{ display: 'flex', gap: 1, marginTop: 1, justifyContent: 'flex-end' }}>
          <Chip
            icon={<PsychologyIcon />}
            label={grokStatus?.available ? "Grok AI Active" : "Grok AI Connecting..."}
            size="small"
            sx={{
              backgroundColor: grokStatus?.available ? 'rgba(0, 153, 255, 0.1)' : 'rgba(255, 170, 0, 0.1)',
              color: grokStatus?.available ? '#0099ff' : '#ffaa00',
              border: grokStatus?.available ? '1px solid rgba(0, 153, 255, 0.3)' : '1px solid rgba(255, 170, 0, 0.3)',
            }}
          />
          <Chip
            icon={<TrendingUpIcon />}
            label={isSystemHealthy ? "35%+ Returns" : "System Initializing"}
            size="small"
            sx={{
              backgroundColor: isSystemHealthy ? 'rgba(0, 255, 136, 0.1)' : 'rgba(255, 170, 0, 0.1)',
              color: isSystemHealthy ? '#00ff88' : '#ffaa00',
              border: isSystemHealthy ? '1px solid rgba(0, 255, 136, 0.3)' : '1px solid rgba(255, 170, 0, 0.3)',
            }}
          />
        </Box>
      </InputContainer>
    </ChatContainer>
  );
};

export default AtlasChatInterface;
