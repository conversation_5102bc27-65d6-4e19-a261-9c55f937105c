import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  TextField,
  IconButton,
  Typography,
  Paper,
  Fade,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  Send as SendIcon,
  Mic as MicIcon,
  AttachFile as AttachFileIcon,
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// A.T.L.A.S. Components (will be created)
// import AtlasChatMessage from './AtlasChatMessage';
// import AtlasTypingIndicator from './AtlasTypingIndicator';
// import AtlasQuickActions from './AtlasQuickActions';

// Services and Types
import { atlasApi } from '../../services/atlasApi';
import { AtlasMessage, ProgressUpdate } from '../../types/atlas';
import { useAtlasWebSocket } from '../../hooks/useAtlasWebSocket';

const ChatContainer = styled(Box)(() => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  background: 'var(--atlas-bg-primary)',
  position: 'relative',
}));

const MessagesContainer = styled(Box)(({ theme }) => ({
  flex: 1,
  overflowY: 'auto',
  padding: theme.spacing(2),
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'var(--atlas-bg-secondary)',
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    background: 'var(--atlas-primary)',
    borderRadius: '4px',
    boxShadow: 'var(--atlas-glow-primary)',
  },
}));

const InputContainer = styled(Paper)(({ theme }) => ({
  margin: theme.spacing(2),
  padding: theme.spacing(2),
  background: 'var(--atlas-gradient-card)',
  border: '1px solid var(--atlas-border-primary)',
  borderRadius: theme.spacing(2),
  backdropFilter: 'blur(10px)',
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    backgroundColor: 'transparent',
    border: '1px solid var(--atlas-border-secondary)',
    borderRadius: theme.spacing(1),
    transition: 'all 0.3s ease',
    '&:hover': {
      border: '1px solid var(--atlas-border-accent)',
      boxShadow: 'var(--atlas-border-glow)',
    },
    '&.Mui-focused': {
      border: '1px solid var(--atlas-primary)',
      boxShadow: 'var(--atlas-glow-primary)',
    },
    '& fieldset': {
      border: 'none',
    },
  },
  '& .MuiInputBase-input': {
    color: 'var(--atlas-text-primary)',
    fontSize: '1rem',
    '&::placeholder': {
      color: 'var(--atlas-text-muted)',
      opacity: 1,
    },
  },
}));

const SendButton = styled(IconButton)(() => ({
  background: 'var(--atlas-gradient-primary)',
  color: 'var(--atlas-bg-primary)',
  width: '48px',
  height: '48px',
  boxShadow: 'var(--atlas-glow-primary)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 0 25px rgba(0, 255, 136, 0.5)',
  },
  '&:disabled': {
    background: 'var(--atlas-bg-tertiary)',
    color: 'var(--atlas-text-muted)',
    boxShadow: 'none',
  },
}));

const ProgressContainer = styled(Box)(() => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  zIndex: 10,
}));

const AtlasChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<AtlasMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentProgress, setCurrentProgress] = useState<ProgressUpdate | null>(null);
  const [sessionId] = useState(() => `atlas-session-${Date.now()}`);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // WebSocket connection for progress updates
  const { connectProgress } = useAtlasWebSocket();

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // Initialize progress WebSocket
  useEffect(() => {
    connectProgress(sessionId);
  }, [sessionId, connectProgress]);

  // Handle progress updates (placeholder for future use)
  // const _handleProgressUpdate = useCallback((update: ProgressUpdate) => {
  //   setCurrentProgress(update);
  //
  //   if (update.status === 'completed' || update.status === 'failed') {
  //     setTimeout(() => {
  //       setCurrentProgress(null);
  //       setIsProcessing(false);
  //     }, 1000);
  //   }
  // }, []);

  // Send message to A.T.L.A.S.
  const sendMessage = useCallback(async () => {
    if (!inputValue.trim() || isProcessing) return;

    const userMessage: AtlasMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);
    setIsProcessing(true);

    try {
      // Send to A.T.L.A.S. API
      const response = await atlasApi.chat(userMessage.content, sessionId);

      if (response.success) {
        const assistantMessage: AtlasMessage = {
          id: `assistant-${Date.now()}`,
          type: 'assistant',
          content: response.data.response,
          timestamp: new Date(),
          metadata: {
            confidence: response.data.confidence,
            grok_enhanced: response.data.grok_enhanced,
            progress_stage: 'completed',
          },
        };

        setMessages(prev => [...prev, assistantMessage]);
      } else {
        throw new Error(response.error || 'Failed to get response');
      }
    } catch (error) {
      console.error('❌ Chat error:', error);
      
      const errorMessage: AtlasMessage = {
        id: `error-${Date.now()}`,
        type: 'system',
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
      setIsProcessing(false);
    }
  }, [inputValue, isProcessing, sessionId]);

  // Handle Enter key press
  const handleKeyPress = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  }, [sendMessage]);

  // Handle quick action selection
  const handleQuickAction = useCallback((action: string) => {
    setInputValue(action);
    inputRef.current?.focus();
  }, []);

  // Welcome message on first load
  useEffect(() => {
    const welcomeMessage: AtlasMessage = {
      id: 'welcome',
      type: 'assistant',
      content: `🚀 **Welcome to A.T.L.A.S. v5.0 Enhanced!**

I'm your advanced trading assistant with Grok AI integration. I can help you with:

• **Trading Analysis** - Get 6-point Stock Market God format recommendations
• **Lee Method Scanning** - Real-time pattern detection and alerts  
• **Market Research** - Live news insights and sentiment analysis
• **Portfolio Management** - Risk assessment and optimization
• **Options Strategies** - Black-Scholes pricing and Greeks analysis
• **Educational Content** - Learn from trading books and strategies

**Try asking me:**
- "Analyze AAPL for me"
- "What are the current Lee Method signals?"
- "Show me market news for tech stocks"
- "Help me understand options trading"

What would you like to explore today?`,
      timestamp: new Date(),
      metadata: {
        grok_enhanced: true,
      },
    };

    setMessages([welcomeMessage]);
  }, []);

  return (
    <ChatContainer>
      {/* Progress Indicator */}
      {currentProgress && (
        <ProgressContainer>
          <LinearProgress
            variant="determinate"
            value={currentProgress.percentage}
            sx={{
              height: '4px',
              backgroundColor: 'rgba(0, 255, 136, 0.1)',
              '& .MuiLinearProgress-bar': {
                background: 'var(--atlas-gradient-primary)',
              },
            }}
          />
          <Box sx={{ padding: 1, backgroundColor: 'var(--atlas-bg-secondary)' }}>
            <Typography variant="caption" color="primary">
              {currentProgress.message} ({currentProgress.percentage}%)
            </Typography>
          </Box>
        </ProgressContainer>
      )}

      {/* Messages Area */}
      <MessagesContainer>
        {messages.map((message) => (
          <Fade in key={message.id} timeout={300}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',
                marginBottom: 2,
              }}
            >
              <Paper
                sx={{
                  maxWidth: '80%',
                  padding: 2,
                  background: message.type === 'user'
                    ? 'var(--atlas-gradient-primary)'
                    : 'var(--atlas-gradient-card)',
                  color: message.type === 'user'
                    ? 'var(--atlas-bg-primary)'
                    : 'var(--atlas-text-primary)',
                  border: '1px solid var(--atlas-border-primary)',
                  borderRadius: message.type === 'user' ? '20px 20px 4px 20px' : '20px 20px 20px 4px',
                  boxShadow: message.type === 'user'
                    ? 'var(--atlas-glow-primary)'
                    : 'none',
                }}
              >
                <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                  {message.content}
                </Typography>
                {message.metadata?.confidence && (
                  <Chip
                    label={`Confidence: ${(message.metadata.confidence * 100).toFixed(1)}%`}
                    size="small"
                    sx={{ marginTop: 1, opacity: 0.8 }}
                  />
                )}
              </Paper>
            </Box>
          </Fade>
        ))}

        {isTyping && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-start', marginBottom: 2 }}>
            <Paper
              sx={{
                padding: 2,
                background: 'var(--atlas-gradient-card)',
                border: '1px solid var(--atlas-border-primary)',
                borderRadius: '20px 20px 20px 4px',
              }}
            >
              <Typography variant="body2" color="primary" className="atlas-pulse">
                A.T.L.A.S. is thinking...
              </Typography>
            </Paper>
          </Box>
        )}
        
        <div ref={messagesEndRef} />
      </MessagesContainer>

      {/* Quick Actions */}
      <Box sx={{ padding: 2, borderTop: '1px solid var(--atlas-border-primary)' }}>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {[
            'Analyze AAPL',
            'Lee Method signals',
            'Market news',
            'Portfolio analysis',
            'Options strategies',
            'Risk assessment'
          ].map((action) => (
            <Chip
              key={action}
              label={action}
              onClick={() => handleQuickAction(action)}
              sx={{
                backgroundColor: 'rgba(0, 255, 136, 0.1)',
                color: '#00ff88',
                border: '1px solid rgba(0, 255, 136, 0.3)',
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: 'rgba(0, 255, 136, 0.2)',
                  boxShadow: 'var(--atlas-border-glow)',
                },
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Input Area */}
      <InputContainer elevation={0}>
        <Box sx={{ display: 'flex', alignItems: 'flex-end', gap: 2 }}>
          {/* Attachment Button */}
          <IconButton
            sx={{ color: 'var(--atlas-text-secondary)' }}
            disabled={isProcessing}
          >
            <AttachFileIcon />
          </IconButton>

          {/* Voice Input Button */}
          <IconButton
            sx={{ color: 'var(--atlas-text-secondary)' }}
            disabled={isProcessing}
          >
            <MicIcon />
          </IconButton>

          {/* Text Input */}
          <StyledTextField
            ref={inputRef}
            fullWidth
            multiline
            maxRows={4}
            placeholder="Ask A.T.L.A.S. anything about trading, markets, or analysis..."
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isProcessing}
            variant="outlined"
          />

          {/* Send Button */}
          <SendButton
            onClick={sendMessage}
            disabled={!inputValue.trim() || isProcessing}
          >
            <SendIcon />
          </SendButton>
        </Box>

        {/* Status Indicators */}
        <Box sx={{ display: 'flex', gap: 1, marginTop: 1, justifyContent: 'flex-end' }}>
          <Chip
            icon={<PsychologyIcon />}
            label="Grok AI Enhanced"
            size="small"
            sx={{
              backgroundColor: 'rgba(0, 153, 255, 0.1)',
              color: '#0099ff',
              border: '1px solid rgba(0, 153, 255, 0.3)',
            }}
          />
          <Chip
            icon={<TrendingUpIcon />}
            label="35%+ Returns"
            size="small"
            sx={{
              backgroundColor: 'rgba(0, 255, 136, 0.1)',
              color: '#00ff88',
              border: '1px solid rgba(0, 255, 136, 0.3)',
            }}
          />
        </Box>
      </InputContainer>
    </ChatContainer>
  );
};

export default AtlasChatInterface;
