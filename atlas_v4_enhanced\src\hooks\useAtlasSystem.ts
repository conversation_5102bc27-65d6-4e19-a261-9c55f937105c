import { useState, useEffect, useCallback } from 'react';
import { atlasApi } from '../services/atlasApi';
import { SystemStatus } from '../types/atlas';

interface AtlasSystemState {
  healthStatus: SystemStatus | null;
  performanceMetrics: any | null;
  grokStatus: any | null;
  isSystemHealthy: boolean;
  lastHealthCheck: Date | null;
  isLoading: boolean;
  error: string | null;
}

export const useAtlasSystem = () => {
  const [state, setState] = useState<AtlasSystemState>({
    healthStatus: null,
    performanceMetrics: null,
    grokStatus: null,
    isSystemHealthy: false,
    lastHealthCheck: null,
    isLoading: true,
    error: null,
  });

  // Check system health
  const checkSystemHealth = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Parallel health checks
      const [healthResponse, performanceResponse, grokResponse] = await Promise.allSettled([
        atlasApi.healthCheck(),
        atlasApi.getPerformanceMetrics(),
        atlasApi.getGrokStatus(),
      ]);

      let healthStatus: SystemStatus | null = null;
      let performanceMetrics: any = null;
      let grokStatus: any = null;
      let isHealthy = false;

      // Process health check
      if (healthResponse.status === 'fulfilled' && healthResponse.value.success) {
        healthStatus = healthResponse.value.data || null;
        // Check for both backend_health and status fields
        isHealthy = healthStatus?.backend_health === 'healthy' || healthStatus?.status === 'healthy';
        console.log('🏥 Health status processed:', { healthStatus, isHealthy });
      }

      // Process performance metrics
      if (performanceResponse.status === 'fulfilled' && performanceResponse.value.success) {
        performanceMetrics = performanceResponse.value.data;
      }

      // Process Grok status
      if (grokResponse.status === 'fulfilled' && grokResponse.value.success) {
        grokStatus = grokResponse.value.data;
      }

      setState(prev => ({
        ...prev,
        healthStatus,
        performanceMetrics,
        grokStatus,
        isSystemHealthy: isHealthy,
        lastHealthCheck: new Date(),
        isLoading: false,
        error: null,
      }));

      console.log('✅ System health check completed:', {
        healthy: isHealthy,
        backend: healthStatus?.backend_health,
        grok: grokStatus?.status,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ System health check failed:', errorMessage);
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
        isSystemHealthy: false,
      }));
    }
  }, []);

  // Get detailed system status
  const getSystemStatus = useCallback(async (): Promise<SystemStatus | null> => {
    try {
      const response = await atlasApi.getSystemStatus();
      if (response.success) {
        return response.data || null;
      }
      return null;
    } catch (error) {
      console.error('❌ Failed to get system status:', error);
      return null;
    }
  }, []);

  // Test API connectivity
  const testApiConnectivity = useCallback(async (): Promise<boolean> => {
    try {
      const response = await atlasApi.healthCheck();
      return response.success && response.data?.backend_health === 'healthy';
    } catch (error) {
      console.error('❌ API connectivity test failed:', error);
      return false;
    }
  }, []);

  // Get Grok integration status
  const getGrokIntegrationStatus = useCallback(async () => {
    try {
      const response = await atlasApi.getGrokStatus();
      return response.success ? response.data : null;
    } catch (error) {
      console.error('❌ Failed to get Grok status:', error);
      return null;
    }
  }, []);

  // Validate all critical endpoints
  const validateCriticalEndpoints = useCallback(async (): Promise<{
    working: string[];
    failed: string[];
  }> => {
    const endpoints = [
      { name: 'health', test: () => atlasApi.healthCheck() },
      { name: 'scanner', test: () => atlasApi.getLeeMethodSignals() },
      { name: 'market_data', test: () => atlasApi.getMarketData('AAPL') },
      { name: 'performance', test: () => atlasApi.getPerformanceMetrics() },
      { name: 'grok', test: () => atlasApi.getGrokStatus() },
    ];

    const results = await Promise.allSettled(
      endpoints.map(async (endpoint) => ({
        name: endpoint.name,
        result: await endpoint.test(),
      }))
    );

    const working: string[] = [];
    const failed: string[] = [];

    results.forEach((result, index) => {
      const endpointName = endpoints[index].name;
      
      if (result.status === 'fulfilled' && result.value.result.success) {
        working.push(endpointName);
      } else {
        failed.push(endpointName);
      }
    });

    console.log('🔍 Endpoint validation:', { working, failed });
    return { working, failed };
  }, []);

  // Initialize system monitoring
  useEffect(() => {
    checkSystemHealth();

    // Set up periodic health checks every 30 seconds
    const healthCheckInterval = setInterval(checkSystemHealth, 30000);

    return () => {
      clearInterval(healthCheckInterval);
    };
  }, [checkSystemHealth]);

  // Refresh system data
  const refreshSystemData = useCallback(() => {
    checkSystemHealth();
  }, [checkSystemHealth]);

  return {
    ...state,
    checkSystemHealth,
    getSystemStatus,
    testApiConnectivity,
    getGrokIntegrationStatus,
    validateCriticalEndpoints,
    refreshSystemData,
  };
};
