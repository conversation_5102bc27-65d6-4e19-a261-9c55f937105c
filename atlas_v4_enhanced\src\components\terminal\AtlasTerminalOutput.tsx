import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Tooltip,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Terminal as TerminalIcon,
  Clear as ClearIcon,
  Download as DownloadIcon,
  Search as SearchIcon,
  Pause as PauseIcon,
  PlayArrow as PlayArrowIcon,

} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Types
interface TerminalMessage {
  id: string;
  timestamp: Date;
  level: 'info' | 'warning' | 'error' | 'success' | 'debug';
  source: string;
  message: string;
  details?: any;
}

interface AtlasTerminalOutputProps {
  messages: TerminalMessage[];
  isLive?: boolean;
  maxMessages?: number;
  showTimestamps?: boolean;
  showSource?: boolean;
  allowSearch?: boolean;
  height?: number;
  title?: string;
}

// Styled Components
const TerminalContainer = styled(Paper)(() => ({
  background: '#0a0a0a',
  border: '1px solid var(--atlas-border-primary)',
  borderRadius: '8px',
  fontFamily: 'Monaco, Consolas, "Courier New", monospace',
  fontSize: '12px',
  overflow: 'hidden',
}));

const TerminalHeader = styled(Box)(({ theme }) => ({
  background: 'var(--atlas-gradient-card)',
  borderBottom: '1px solid var(--atlas-border-primary)',
  padding: theme.spacing(1, 2),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
}));

const TerminalContent = styled(Box)<{ height: number }>(({ height }) => ({
  height: `${height}px`,
  overflowY: 'auto',
  padding: '8px',
  backgroundColor: '#0a0a0a',
  color: '#ffffff',
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'rgba(255, 255, 255, 0.1)',
  },
  '&::-webkit-scrollbar-thumb': {
    background: 'var(--atlas-accent-primary)',
    borderRadius: '3px',
  },
}));

const MessageLine = styled(Box)<{ level: string }>(({ level }) => ({
  display: 'flex',
  alignItems: 'flex-start',
  gap: '8px',
  marginBottom: '2px',
  padding: '2px 4px',
  borderRadius: '2px',
  fontSize: '12px',
  lineHeight: '1.4',
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  color: 
    level === 'error' ? '#ff5252' :
    level === 'warning' ? '#ffaa00' :
    level === 'success' ? '#00ff88' :
    level === 'info' ? '#0099ff' :
    level === 'debug' ? '#888888' :
    '#ffffff',
}));

const AtlasTerminalOutput: React.FC<AtlasTerminalOutputProps> = ({
  messages,
  isLive = true,
  maxMessages = 1000,
  showTimestamps = true,
  showSource = true,
  allowSearch = true,
  height = 300,
  title = "System Terminal",
}) => {
  const [filteredMessages, setFilteredMessages] = useState<TerminalMessage[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isPaused, setIsPaused] = useState(false);
  const [showDebug, setShowDebug] = useState(false);
  const [levelFilter, setLevelFilter] = useState<string[]>(['info', 'warning', 'error', 'success']);
  
  const contentRef = useRef<HTMLDivElement>(null);
  const shouldAutoScroll = useRef(true);

  // Filter messages based on search and level filters
  useEffect(() => {
    let filtered = messages.slice(-maxMessages);

    // Apply level filter
    if (!showDebug) {
      filtered = filtered.filter(msg => msg.level !== 'debug');
    }
    filtered = filtered.filter(msg => levelFilter.includes(msg.level));

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(msg => 
        msg.message.toLowerCase().includes(term) ||
        msg.source.toLowerCase().includes(term)
      );
    }

    setFilteredMessages(filtered);
  }, [messages, searchTerm, levelFilter, showDebug, maxMessages]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (shouldAutoScroll.current && contentRef.current && !isPaused) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    }
  }, [filteredMessages, isPaused]);

  // Handle scroll to detect if user scrolled up
  const handleScroll = useCallback(() => {
    if (contentRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = contentRef.current;
      shouldAutoScroll.current = scrollTop + clientHeight >= scrollHeight - 10;
    }
  }, []);

  // Format timestamp
  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('en-US', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      // fractionalSecondDigits: 3  // Not supported in all browsers
    });
  };

  // Get level indicator
  const getLevelIndicator = (level: string) => {
    switch (level) {
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'success': return '✅';
      case 'info': return 'ℹ️';
      case 'debug': return '🔍';
      default: return '•';
    }
  };

  // Clear terminal
  const handleClear = () => {
    setFilteredMessages([]);
  };

  // Download logs
  const handleDownload = () => {
    const logContent = filteredMessages.map(msg => 
      `[${formatTimestamp(msg.timestamp)}] ${msg.level.toUpperCase()} [${msg.source}] ${msg.message}`
    ).join('\n');
    
    const blob = new Blob([logContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `atlas-logs-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Toggle level filter
  const toggleLevelFilter = (level: string) => {
    setLevelFilter(prev => 
      prev.includes(level) 
        ? prev.filter(l => l !== level)
        : [...prev, level]
    );
  };

  return (
    <TerminalContainer elevation={2}>
      {/* Header */}
      <TerminalHeader>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TerminalIcon fontSize="small" />
          <Typography variant="subtitle2" color="primary">
            {title}
          </Typography>
          <Chip
            label={isLive && !isPaused ? "LIVE" : "PAUSED"}
            size="small"
            color={isLive && !isPaused ? "success" : "warning"}
            sx={{ fontSize: '0.7rem' }}
          />
          <Chip
            label={`${filteredMessages.length} lines`}
            size="small"
            variant="outlined"
            sx={{ fontSize: '0.7rem' }}
          />
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* Level Filters */}
          {['error', 'warning', 'info', 'success'].map(level => (
            <Chip
              key={level}
              label={level.toUpperCase()}
              size="small"
              onClick={() => toggleLevelFilter(level)}
              color={levelFilter.includes(level) ? "primary" : "default"}
              sx={{ 
                fontSize: '0.6rem', 
                height: 20,
                cursor: 'pointer',
                opacity: levelFilter.includes(level) ? 1 : 0.5
              }}
            />
          ))}

          <FormControlLabel
            control={
              <Switch
                checked={showDebug}
                onChange={(e) => setShowDebug(e.target.checked)}
                size="small"
              />
            }
            label="Debug"
            sx={{ margin: 0, '& .MuiFormControlLabel-label': { fontSize: '0.7rem' } }}
          />

          <Tooltip title={isPaused ? "Resume" : "Pause"}>
            <IconButton
              size="small"
              onClick={() => setIsPaused(!isPaused)}
              sx={{ color: 'var(--atlas-text-secondary)' }}
            >
              {isPaused ? <PlayArrowIcon /> : <PauseIcon />}
            </IconButton>
          </Tooltip>

          <Tooltip title="Clear">
            <IconButton
              size="small"
              onClick={handleClear}
              sx={{ color: 'var(--atlas-text-secondary)' }}
            >
              <ClearIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Download Logs">
            <IconButton
              size="small"
              onClick={handleDownload}
              sx={{ color: 'var(--atlas-text-secondary)' }}
            >
              <DownloadIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </TerminalHeader>

      {/* Search Bar */}
      {allowSearch && (
        <Box sx={{ padding: 1, borderBottom: '1px solid var(--atlas-border-primary)' }}>
          <TextField
            fullWidth
            size="small"
            placeholder="Search logs..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                fontSize: '0.8rem',
                backgroundColor: 'rgba(255, 255, 255, 0.05)',
              },
            }}
          />
        </Box>
      )}

      {/* Terminal Content */}
      <TerminalContent
        ref={contentRef}
        height={height}
        onScroll={handleScroll}
      >
        {filteredMessages.map((message) => (
          <MessageLine key={message.id} level={message.level}>
            {showTimestamps && (
              <Typography
                component="span"
                sx={{ 
                  color: '#666666', 
                  fontSize: '11px',
                  minWidth: '80px',
                  fontFamily: 'inherit'
                }}
              >
                {formatTimestamp(message.timestamp)}
              </Typography>
            )}
            
            <Typography
              component="span"
              sx={{ 
                fontSize: '11px',
                minWidth: '16px',
                fontFamily: 'inherit'
              }}
            >
              {getLevelIndicator(message.level)}
            </Typography>

            {showSource && (
              <Typography
                component="span"
                sx={{ 
                  color: '#888888',
                  fontSize: '11px',
                  minWidth: '80px',
                  fontFamily: 'inherit'
                }}
              >
                [{message.source}]
              </Typography>
            )}

            <Typography
              component="span"
              sx={{ 
                fontSize: '11px',
                flex: 1,
                fontFamily: 'inherit',
                wordBreak: 'break-word'
              }}
            >
              {message.message}
            </Typography>
          </MessageLine>
        ))}
        
        {filteredMessages.length === 0 && (
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center', 
            height: '100%',
            color: '#666666'
          }}>
            <Typography variant="body2">
              {searchTerm ? 'No matching logs found' : 'No logs available'}
            </Typography>
          </Box>
        )}
      </TerminalContent>
    </TerminalContainer>
  );
};

export default AtlasTerminalOutput;
